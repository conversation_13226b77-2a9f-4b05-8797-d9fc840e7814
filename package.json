{"name": "yyhis-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "build": "npm run build:info:prod && vite build --mode production", "build:dev": "npm run build:info:dev && vite build --mode development", "build:test": "npm run build:info:test && vite build --mode test", "build:prod": "npm run build:info:prod && vite build --mode production", "build:info": "node scripts/build-info.js", "build:info:dev": "node scripts/build-info.js development", "build:info:test": "node scripts/build-info.js test", "build:info:prod": "node scripts/build-info.js production", "env:compare": "node scripts/compare-envs.js", "preview": "vite preview --mode production", "preview:test": "vite preview --mode test"}, "dependencies": {"axios": "^1.10.0", "element-plus": "^2.10.3", "pinia": "^2.3.1", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "terser": "^5.43.1", "vite": "^7.0.0"}}