/**
 * 构建信息验证脚本
 * 用于验证构建时使用的环境配置
 */

import { loadEnv } from 'vite'
import { fileURLToPath } from 'url'
import { dirname, resolve } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const root = resolve(__dirname, '..')

// 获取构建模式
const mode = process.env.NODE_ENV || process.argv[2] || 'production'

console.log('🔧 构建信息验证')
console.log('=' .repeat(50))

// 加载环境变量
const env = loadEnv(mode, root, '')

console.log(`📋 构建模式: ${mode}`)
console.log(`📁 项目根目录: ${root}`)
console.log(`🌐 API基础URL: ${env.VITE_API_BASE_URL}`)
console.log(`🏥 应用标题: ${env.VITE_APP_TITLE}`)
console.log(`📦 应用版本: ${env.VITE_APP_VERSION}`)
console.log(`🔗 后端URL: ${env.VITE_BACKEND_URL}`)
console.log(`⏱️ API超时: ${env.VITE_API_TIMEOUT}ms`)

console.log('\n🔍 环境变量检查:')
const requiredVars = [
  'VITE_API_BASE_URL',
  'VITE_APP_TITLE',
  'VITE_APP_VERSION'
]

let allValid = true
requiredVars.forEach(varName => {
  const value = env[varName]
  const isValid = value && value.trim() !== ''
  console.log(`  ${isValid ? '✅' : '❌'} ${varName}: ${value || '(未设置)'}`)
  if (!isValid) allValid = false
})

console.log('\n📊 构建配置总结:')
if (mode === 'production') {
  console.log('🚀 生产环境构建')
  console.log('  - 使用 .env.production 配置')
  console.log('  - API地址应为生产环境地址')
  console.log('  - 不使用代理配置')
} else if (mode === 'test') {
  console.log('🧪 测试环境构建')
  console.log('  - 使用 .env.test 配置')
  console.log('  - API地址应为测试环境地址')
} else if (mode === 'development') {
  console.log('🛠️ 开发环境构建')
  console.log('  - 使用 .env.development 配置')
  console.log('  - 可能包含开发工具和调试信息')
}

if (!allValid) {
  console.log('\n⚠️ 警告: 某些必需的环境变量未设置')
  process.exit(1)
} else {
  console.log('\n✅ 环境配置验证通过')
}

console.log('=' .repeat(50))
