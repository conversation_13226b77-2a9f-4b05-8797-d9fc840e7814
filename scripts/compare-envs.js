/**
 * 环境配置对比脚本
 * 对比不同环境的配置差异
 */

import { loadEnv } from 'vite'
import { fileURLToPath } from 'url'
import { dirname, resolve } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const root = resolve(__dirname, '..')

const environments = ['development', 'test', 'production']
const configKeys = [
  'VITE_APP_TITLE',
  'VITE_APP_ENV',
  'VITE_API_BASE_URL',
  'VITE_BACKEND_URL',
  'VITE_API_TIMEOUT',
  'VITE_APP_VERSION'
]

console.log('🔍 环境配置对比表')
console.log('=' .repeat(80))

// 加载所有环境的配置
const envConfigs = {}
environments.forEach(env => {
  envConfigs[env] = loadEnv(env, root, '')
})

// 创建对比表
console.log('📊 配置项对比:')
console.log('')

// 表头
const header = '| 配置项 | 开发环境 | 测试环境 | 生产环境 |'
const separator = '|--------|----------|----------|----------|'
console.log(header)
console.log(separator)

// 配置项对比
configKeys.forEach(key => {
  const dev = envConfigs.development[key] || '(未设置)'
  const test = envConfigs.test[key] || '(未设置)'
  const prod = envConfigs.production[key] || '(未设置)'
  
  console.log(`| ${key} | ${dev} | ${test} | ${prod} |`)
})

console.log('')
console.log('🔧 环境特性对比:')
console.log('')

const features = [
  {
    name: 'API访问方式',
    dev: '代理 (/api -> localhost:6596)',
    test: '直接访问测试服务器',
    prod: '直接访问生产服务器'
  },
  {
    name: 'CORS处理',
    dev: 'Vite代理自动处理',
    test: '需要后端配置CORS',
    prod: '需要后端配置CORS'
  },
  {
    name: '调试信息',
    dev: '包含详细日志',
    test: '包含部分日志',
    prod: '最小化日志'
  },
  {
    name: '构建优化',
    dev: '快速构建，包含源码映射',
    test: '优化构建，包含源码映射',
    prod: '完全优化，压缩代码'
  }
]

const featureHeader = '| 特性 | 开发环境 | 测试环境 | 生产环境 |'
console.log(featureHeader)
console.log(separator)

features.forEach(feature => {
  console.log(`| ${feature.name} | ${feature.dev} | ${feature.test} | ${feature.prod} |`)
})

console.log('')
console.log('💡 使用建议:')
console.log('  🛠️  开发: npm run dev (使用代理，热重载)')
console.log('  🧪 测试: npm run build:test (构建测试版本)')
console.log('  🚀 生产: npm run build (构建生产版本)')
console.log('')
console.log('=' .repeat(80))
