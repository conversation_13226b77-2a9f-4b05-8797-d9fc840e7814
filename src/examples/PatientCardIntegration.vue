<!--
  患者卡片点击集成示例
  演示如何在点击患者卡片时调用第三方SDK
-->
<template>
  <div class="patient-list">
    <h2>患者列表</h2>
    
    <!-- 集成状态提示 -->
    <div class="integration-status">
      <el-alert 
        v-if="!sdkReady" 
        title="质控系统未就绪，请先初始化" 
        type="warning" 
        show-icon 
      />
      <el-alert 
        v-else 
        title="质控系统已就绪，点击患者卡片即可发送数据" 
        type="success" 
        show-icon 
      />
    </div>

    <!-- 患者卡片列表 -->
    <div class="patient-cards">
      <div 
        v-for="patient in patientList" 
        :key="patient.id"
        class="patient-card"
        :class="{ 'sending': sendingPatients.includes(patient.id) }"
        @click="handlePatientCardClick(patient)"
      >
        <!-- 加载状态 -->
        <div v-if="sendingPatients.includes(patient.id)" class="loading-overlay">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>发送中...</span>
        </div>

        <!-- 患者信息 -->
        <div class="patient-info">
          <div class="patient-header">
            <h3>{{ patient.name }}</h3>
            <el-tag :type="getVisitTypeTag(patient.visitType)">
              {{ patient.visitType }}
            </el-tag>
          </div>
          
          <div class="patient-details">
            <p><strong>性别:</strong> {{ patient.gender }}</p>
            <p><strong>年龄:</strong> {{ patient.age }}岁</p>
            <p><strong>科室:</strong> {{ patient.deptName }}</p>
            <p v-if="patient.bedName"><strong>床号:</strong> {{ patient.bedName }}</p>
            <p><strong>就诊号:</strong> {{ patient.visitSn }}</p>
          </div>

          <!-- 发送状态 -->
          <div class="send-status">
            <el-icon v-if="patient.lastSendSuccess === true" color="#67c23a">
              <SuccessFilled />
            </el-icon>
            <el-icon v-else-if="patient.lastSendSuccess === false" color="#f56c6c">
              <CircleCloseFilled />
            </el-icon>
            <span v-if="patient.lastSendTime" class="send-time">
              {{ formatTime(patient.lastSendTime) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量操作 -->
    <div class="batch-actions">
      <el-button 
        type="primary" 
        @click="batchSendPatients"
        :disabled="!sdkReady || batchSending"
        :loading="batchSending"
      >
        批量发送所有患者
      </el-button>
      <el-button @click="initializeSDK" :loading="initializing">
        重新初始化SDK
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Loading, SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import thirdPartyIntegration from '@/services/thirdPartyIntegration'
import { PatientDataAdapter } from '@/utils/patientDataAdapter'

// 响应式数据
const sdkReady = ref(false)
const initializing = ref(false)
const sendingPatients = ref([])
const batchSending = ref(false)

// 模拟患者数据
const patientList = reactive([
  {
    id: '00931222',
    name: '张三',
    gender: '男',
    age: 45,
    visitType: '住院',
    deptName: '肿瘤内科',
    bedName: '28床',
    visitSn: '00931222|4623477|1住院',
    dateOfBirth: '1978-05-15',
    phoneNo: '13800138000',
    admissionDatetime: '2023-04-16 07:11:07',
    lastSendSuccess: null,
    lastSendTime: null
  },
  {
    id: 'OP001234',
    name: '李四',
    gender: '女',
    age: 38,
    visitType: '门诊',
    deptName: '肿瘤科',
    bedName: null,
    visitSn: 'OP001234|20230416001|2门诊',
    dateOfBirth: '1985-03-20',
    phoneNo: '13900139000',
    visitDatetime: '2023-04-16 09:30:00',
    lastSendSuccess: null,
    lastSendTime: null
  },
  {
    id: '00931223',
    name: '王五',
    gender: '男',
    age: 52,
    visitType: '住院',
    deptName: '肿瘤外科',
    bedName: '15床',
    visitSn: '00931223|4623478|1住院',
    dateOfBirth: '1971-08-20',
    phoneNo: '13700137000',
    admissionDatetime: '2023-04-15 14:30:00',
    lastSendSuccess: null,
    lastSendTime: null
  }
])

// 方法
const initializeSDK = async () => {
  initializing.value = true
  try {
    // 检查是否已初始化
    const status = thirdPartyIntegration.getStatus()
    if (status.isInitialized) {
      sdkReady.value = true
      ElMessage.success('SDK已就绪')
      return
    }

    // 初始化配置（实际使用时需要从配置中获取）
    const config = {
      sdkUrl: 'http://123.127.210.51:8094/client_app_iframe/index.js',
      appKey: 'ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09', // 已提供
      appSecretKey: 'your_app_secret_key_here', // 待项目经理提供
      deptId: '0001',
      doctorId: '0002',
      qualityTarget: '2',
      linkType: '2'
    }

    await thirdPartyIntegration.initialize(config)
    sdkReady.value = true
    ElMessage.success('SDK初始化成功')
  } catch (error) {
    console.error('SDK初始化失败:', error)
    ElMessage.error('SDK初始化失败: ' + error.message)
  } finally {
    initializing.value = false
  }
}

/**
 * 核心方法：处理患者卡片点击
 * 这里就是调用他们SDK的地方
 */
const handlePatientCardClick = async (patient) => {
  if (!sdkReady.value) {
    ElMessage.warning('SDK未就绪，请先初始化')
    return
  }

  // 防止重复发送
  if (sendingPatients.value.includes(patient.id)) {
    return
  }

  sendingPatients.value.push(patient.id)

  try {
    console.log(`🚀 点击患者卡片: ${patient.name}，开始发送数据...`)

    // 1. 构造符合他们SDK要求的患者数据格式
    const patientData = buildPatientDataForSDK(patient)

    // 2. 调用他们的SDK方法 _clientAppSDK.history()
    await thirdPartyIntegration.sendPatientData(patientData)

    // 3. 更新发送状态
    patient.lastSendSuccess = true
    patient.lastSendTime = new Date()

    console.log(`✅ 患者 ${patient.name} 数据发送成功`)
    ElMessage.success(`患者 ${patient.name} 数据已发送到质控系统`)

  } catch (error) {
    console.error(`❌ 患者 ${patient.name} 数据发送失败:`, error)
    
    // 更新发送状态
    patient.lastSendSuccess = false
    patient.lastSendTime = new Date()
    
    ElMessage.error(`发送失败: ${error.message}`)
  } finally {
    // 移除loading状态
    const index = sendingPatients.value.indexOf(patient.id)
    if (index > -1) {
      sendingPatients.value.splice(index, 1)
    }
  }
}

/**
 * 构造符合SDK要求的患者数据格式
 * 这个格式必须严格按照文档要求
 */
const buildPatientDataForSDK = (patient) => {
  // 根据就诊类型确定表代码
  const tableCode = patient.visitType === '门诊' ? 'b12_1' : 'b02_1'

  return {
    patientId: patient.id,
    visitSn: patient.visitSn,
    dataPacket: [{
      tableCode: tableCode,
      data: [{
        // 必填字段
        patient_id: patient.id,
        visit_sn: patient.visitSn,
        visit_type: patient.visitType,
        hospital_code: "25ab66f1a2a2421e9b45de3339c17a53",
        hospital_name: "测试医院",
        visit_doctor_no: "001346",
        visit_doctor_name: "张医生",
        name: patient.name,
        gender: patient.gender,
        date_of_birth: patient.dateOfBirth,
        occupation_code: "",
        occupation_name: "其他",
        
        // 根据就诊类型添加特定字段
        ...(patient.visitType === '住院' ? {
          medical_record_no: patient.id,
          inpatient_no: patient.id.substring(0, 6),
          admission_datetime: patient.admissionDatetime,
          admission_dept_code: "034",
          admission_dept_name: patient.deptName,
          current_bed_name: patient.bedName || ""
        } : {
          outpatient_no: patient.id,
          visit_datetime: patient.visitDatetime,
          regis_sn: patient.id + "_reg",
          regis_datetime: patient.visitDatetime,
          regis_dept_code: "15",
          regis_dept_name: patient.deptName
        }),

        // 其他默认字段
        nationality: "中国",
        ethnicity: "汉族",
        phone_no: patient.phoneNo,
        record_status: "1",
        record_datetime: new Date().toISOString().replace('T', ' ').substring(0, 19)
      }]
    }]
  }
}

const batchSendPatients = async () => {
  if (!sdkReady.value) {
    ElMessage.warning('SDK未就绪，请先初始化')
    return
  }

  const result = await ElMessageBox.confirm(
    `确定要批量发送 ${patientList.length} 个患者的数据到质控系统吗？`,
    '批量发送确认',
    { type: 'warning' }
  ).catch(() => false)

  if (!result) return

  batchSending.value = true
  let successCount = 0
  let failCount = 0

  try {
    for (const patient of patientList) {
      try {
        await handlePatientCardClick(patient)
        successCount++
        // 批次间延迟，避免过于频繁
        await new Promise(resolve => setTimeout(resolve, 500))
      } catch (error) {
        failCount++
        console.error(`批量发送失败 - 患者: ${patient.name}`, error)
      }
    }

    ElMessage.success(`批量发送完成！成功: ${successCount}, 失败: ${failCount}`)
  } finally {
    batchSending.value = false
  }
}

const getVisitTypeTag = (visitType) => {
  const tagMap = {
    '住院': 'danger',
    '门诊': 'success',
    '体检': 'info'
  }
  return tagMap[visitType] || 'info'
}

const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

// 生命周期
onMounted(async () => {
  await initializeSDK()
})
</script>

<style scoped>
.patient-list {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.integration-status {
  margin-bottom: 20px;
}

.patient-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.patient-card {
  position: relative;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.patient-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.patient-card.sending {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  z-index: 10;
}

.loading-overlay .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
  color: #409eff;
}

.patient-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.patient-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
}

.patient-details p {
  margin: 5px 0;
  color: #606266;
  font-size: 14px;
}

.send-status {
  display: flex;
  align-items: center;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.send-time {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.batch-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .patient-cards {
    grid-template-columns: 1fr;
  }
  
  .batch-actions {
    flex-direction: column;
  }
}
</style>
