/**
 * API服务统一导出
 * 提供所有API服务的统一入口
 */

// 导入所有服务
import DepartmentService from './services/departmentService'
import PatientService from './services/patientService'
import AdmissionRecordService from './services/admissionRecordService'
import FirstCourseRecordService from './services/firstCourseRecordService'
import ConsultationRecordService from './services/consultationRecordService'
import PreoperativeDiscussionService from './services/preoperativeDiscussionService'
import PreoperativeSummaryService from './services/preoperativeSummaryService'
import InvasiveProcedureService from './services/invasiveProcedureService'
import SurgeryRecordService from './services/surgeryRecordService'
import PostoperativeFirstCourseRecordService from './services/postoperativeFirstCourseRecordService'
import DischargeRecordService from './services/dischargeRecordService'
import DailyProgressRecordService from './services/dailyProgressRecordService'
import TnmStagingRecordService from './services/tnmStagingRecordService'
import DiagnosisInfoService from './services/diagnosisInfoService'
import MedicalOrderService from './services/medicalOrderService'
import LabReportService from './services/labReportService'
import ExamReportService from './services/examReportService'
import PathologyReportService from './services/pathologyReportService'
import MolecularPathologyService from './services/molecularPathologyService'
import CountService from './services/countService'
import DictionaryService from './services/dictionaryService'
import { ThirdPartyIntegrationService } from './services/thirdPartyIntegrationService'

// 导入端点定义
import endpoints from './endpoints'

// 导入请求客户端
import request from '@/utils/request'

/**
 * API服务对象
 * 包含所有业务相关的API服务
 */
export const apiServices = {
  // 科室服务
  department: DepartmentService,

  // 患者服务
  patient: PatientService,

  // 入院记录服务
  admissionRecord: AdmissionRecordService,

  // 首次病程记录服务
  firstCourseRecord: FirstCourseRecordService,

  // 会诊记录服务
  consultationRecord: ConsultationRecordService,

  // 术前讨论服务
  preoperativeDiscussion: PreoperativeDiscussionService,

  // 术前小结服务
  preoperativeSummary: PreoperativeSummaryService,

  // 有创操作记录服务
  invasiveProcedure: InvasiveProcedureService,

  // 手术记录服务
  surgeryRecord: SurgeryRecordService,

  // 术后首次病程记录服务
  postoperativeFirstCourseRecord: PostoperativeFirstCourseRecordService,

  // 出院记录服务
  dischargeRecord: DischargeRecordService,

  // 日常病程记录服务
  dailyProgressRecord: DailyProgressRecordService,

  // TNM分期记录服务
  tnmStagingRecord: TnmStagingRecordService,

  // 诊断信息服务
  diagnosisInfo: DiagnosisInfoService,

  // 医嘱信息服务
  medicalOrder: MedicalOrderService,

  // 检验报告服务
  labReport: LabReportService,

  // 检查报告服务
  examReport: ExamReportService,

  // 病理报告服务
  pathologyReport: PathologyReportService,

  // 分子病理报告服务
  molecularPathology: MolecularPathologyService,

  // 统一计数服务
  count: CountService,

  // 字典服务
  dictionary: DictionaryService,

  // 第三方集成服务
  thirdPartyIntegration: ThirdPartyIntegrationService,

  // 后续可以添加更多服务
  // user: UserService,
  // system: SystemService,
}

/**
 * 快捷API调用方法
 * 提供更简洁的API调用方式
 */
export const api = {
  // 科室相关
  getDepartmentStats: (source) => DepartmentService.getStats(source),
  getDepartmentList: (params) => DepartmentService.getList(params),
  getDepartmentDetail: (id) => DepartmentService.getDetail(id),

  // 患者相关
  getPatientsByDepartment: (departmentCode, source) => PatientService.getByDepartment(departmentCode, source),
  getPatientList: (params) => PatientService.getList(params),
  getPatientDetail: (id) => PatientService.getDetail(id),

  // 入院记录相关
  getAdmissionRecordDetail: (visitSn) => AdmissionRecordService.getDetail(visitSn),
  saveAdmissionRecord: (data) => AdmissionRecordService.save(data),
  deleteAdmissionRecord: (visitSn) => AdmissionRecordService.delete(visitSn),

  // 首次病程记录相关
  getFirstCourseRecordDetail: (visitSn) => FirstCourseRecordService.getDetail(visitSn),
  saveFirstCourseRecord: (data) => FirstCourseRecordService.save(data),
  deleteFirstCourseRecord: (visitSn) => FirstCourseRecordService.delete(visitSn),

  // 会诊记录相关
  getConsultationRecordDetail: (visitSn) => ConsultationRecordService.getDetail(visitSn),
  saveConsultationRecord: (data) => ConsultationRecordService.save(data),
  deleteConsultationRecord: (visitSn) => ConsultationRecordService.delete(visitSn),

  // 术前讨论相关
  getPreoperativeDiscussionDetail: (visitSn) => PreoperativeDiscussionService.getDetail(visitSn),
  savePreoperativeDiscussion: (data) => PreoperativeDiscussionService.save(data),
  deletePreoperativeDiscussion: (visitSn) => PreoperativeDiscussionService.delete(visitSn),

  // 术前小结相关
  getPreoperativeSummaryDetail: (visitSn) => PreoperativeSummaryService.getDetail(visitSn),
  savePreoperativeSummary: (data) => PreoperativeSummaryService.save(data),
  deletePreoperativeSummary: (visitSn) => PreoperativeSummaryService.delete(visitSn),

  // 有创操作记录相关
  getInvasiveProcedureDetail: (visitSn) => InvasiveProcedureService.getDetail(visitSn),
  saveInvasiveProcedure: (data) => InvasiveProcedureService.save(data),
  deleteInvasiveProcedure: (visitSn) => InvasiveProcedureService.delete(visitSn),

  // 手术记录相关
  getSurgeryRecordDetail: (visitSn) => SurgeryRecordService.getDetail(visitSn),
  saveSurgeryRecord: (data) => SurgeryRecordService.save(data),
  deleteSurgeryRecord: (visitSn) => SurgeryRecordService.delete(visitSn),

  // 术后首次病程记录相关
  getPostoperativeFirstCourseRecordDetail: (visitSn) => PostoperativeFirstCourseRecordService.getDetail(visitSn),
  savePostoperativeFirstCourseRecord: (data) => PostoperativeFirstCourseRecordService.save(data),
  deletePostoperativeFirstCourseRecord: (visitSn) => PostoperativeFirstCourseRecordService.delete(visitSn),

  // 出院记录相关
  getDischargeRecordDetail: (visitSn) => DischargeRecordService.getDetail(visitSn),
  saveDischargeRecord: (data) => DischargeRecordService.save(data),
  deleteDischargeRecord: (visitSn) => DischargeRecordService.delete(visitSn),

  // 日常病程记录相关
  getDailyProgressRecordList: (visitSn) => DailyProgressRecordService.getList(visitSn),
  getDailyProgressRecordDetail: (recordSn) => DailyProgressRecordService.getDetail(recordSn),
  saveDailyProgressRecord: (data) => DailyProgressRecordService.save(data),
  deleteDailyProgressRecord: (recordSn) => DailyProgressRecordService.delete(recordSn),
  getDailyProgressRecordCount: (visitSn) => DailyProgressRecordService.getCount(visitSn),

  // 诊断信息相关
  getDiagnosisInfoList: (visitSn) => DiagnosisInfoService.getList(visitSn),
  getDiagnosisInfoDetail: (diagId, diagSource) => DiagnosisInfoService.getDetail(diagId, diagSource),
  saveDiagnosisInfo: (data) => DiagnosisInfoService.save(data),
  updateDiagnosisInfo: (diagId, diagSource, data) => DiagnosisInfoService.update(diagId, diagSource, data),
  deleteDiagnosisInfo: (diagId, diagSource) => DiagnosisInfoService.delete(diagId, diagSource),
  getDiagnosisInfoCount: (visitSn) => DiagnosisInfoService.getCount(visitSn),

  // 医嘱信息相关
  getMedicalOrderList: (visitSn) => MedicalOrderService.getList(visitSn),
  getMedicalOrderDetail: (orderSn) => MedicalOrderService.getDetail(orderSn),
  saveMedicalOrder: (data) => MedicalOrderService.save(data),
  updateMedicalOrder: (orderSn, data) => MedicalOrderService.update(orderSn, data),
  deleteMedicalOrder: (orderSn) => MedicalOrderService.delete(orderSn),
  getMedicalOrderCount: (visitSn) => MedicalOrderService.getCount(visitSn),

  // 检验报告相关
  getLabReportMainList: (visitSn) => LabReportService.getMainList(visitSn),
  getLabReportDetailList: (labSn) => LabReportService.getDetailList(labSn),
  getLabReportCount: (visitSn) => LabReportService.getCount(visitSn),

  // 检查报告相关
  getExamReportList: (visitSn) => ExamReportService.getList(visitSn),
  updateExamReport: (data) => ExamReportService.update(data),
  deleteExamReport: (id) => ExamReportService.delete(id),
  getExamReportCount: (visitSn) => ExamReportService.getCount(visitSn),

  // 病理报告相关
  getPathologyReportList: (visitSn) => PathologyReportService.getList(visitSn),
  updatePathologyReport: (data) => PathologyReportService.update(data),
  deletePathologyReport: (id) => PathologyReportService.delete(id),
  getPathologyReportCount: (visitSn) => PathologyReportService.getCount(visitSn),

  // 分子病理报告相关
  getMolecularPathologyList: (visitSn) => MolecularPathologyService.getList(visitSn),
  updateMolecularPathology: (data) => MolecularPathologyService.update(data),
  deleteMolecularPathology: (id) => MolecularPathologyService.delete(id),
  getMolecularPathologyCount: (visitSn) => MolecularPathologyService.getCount(visitSn),

  // 字典相关
  getDictionaryByType: (dictType) => DictionaryService.getByType(dictType),
  getInviteDepartments: () => DictionaryService.getInviteDepartments(),
  getDiagnosisNames: () => DictionaryService.getDiagnosisNames(),
  getDiagnosisNamesByPage: (params) => DictionaryService.getDiagnosisNamesByPage(params),
  getMedicalOrderNamesByPage: (params) => DictionaryService.getMedicalOrderNamesByPage(params),
  getDiagnosisTypes: () => DictionaryService.getDiagnosisTypes(),
  getYesOrNo: () => DictionaryService.getYesOrNo(),

  // 第三方集成相关
  getPatientHistory: (visitSn) => ThirdPartyIntegrationService.getPatientHistory(visitSn),

  // 通用请求方法（用于特殊情况）
  get: (url, config) => request.get(url, config),
  post: (url, data, config) => request.post(url, data, config),
  put: (url, data, config) => request.put(url, data, config),
  delete: (url, config) => request.delete(url, config),
}

// 导出端点定义（供需要时使用）
export { endpoints }

// 导出请求客户端（供需要时使用）
export { request }

// 默认导出
export default {
  services: apiServices,
  api,
  endpoints,
  request
}
