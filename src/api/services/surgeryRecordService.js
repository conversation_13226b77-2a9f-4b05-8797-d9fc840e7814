/**
 * 手术记录相关API服务
 */

import request from '@/utils/request'
import { surgeryRecordApi } from '@/api/endpoints'

/**
 * 手术记录服务类
 */
export class SurgeryRecordService {
  // 防重复调用的Promise缓存
  static detailPromiseCache = new Map()

  /**
   * 获取手术记录详情
   * @param {string|number} visitSn - 就诊唯一标识号
   * @returns {Promise<Object>} 手术记录详情
   */
  static async getDetail(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    const cacheKey = String(visitSn)

    // 如果正在请求相同visitSn的数据，返回同一个Promise避免重复请求
    if (this.detailPromiseCache.has(cacheKey)) {
      console.log(`手术记录详情 ${visitSn} 请求进行中，等待现有请求完成...`)
      return await this.detailPromiseCache.get(cacheKey)
    }

    // 创建新的请求Promise
    const fetchPromise = (async () => {
      try {
        console.log(`正在获取手术记录详情，visitSn: ${visitSn}`)
        const response = await request.get(surgeryRecordApi.getDetail(visitSn))
        console.log(`手术记录详情获取成功，visitSn: ${visitSn}`)
        
        // 处理响应数据，确保字段映射正确
        const data = response.data || response || {}
        
        // 返回标准化的数据格式
        return {
          // 基础信息
          recordSn: data.recordSn || '',
          visitSn: data.visitSn || visitSn,
          recordDatetime: data.recordDatetime || '',
          recordUpdateDatetime: data.recordUpdateDatetime || '',
          
          // 手术记录特有字段
          preOperativeDiagnosis: data.preOperativeDiagnosis || '',
          postOperationDiagnosis: data.postOperationDiagnosis || '',
          surgeryName: data.surgeryName || '',
          anesthesiaMethod: data.anesthesiaMethod || '',
          surgeryStarttime: data.surgeryStarttime || '',
          surgeryEndtime: data.surgeryEndtime || '',
          surgeryProcess: data.surgeryProcess || '',
          
          // 其他可能的字段
          patientId: data.patientId || '',
          inpatientNo: data.inpatientNo || '',
          signatureDoctor: data.signatureDoctor || '',
          hospitalCode: data.hospitalCode || '',
          hospitalName: data.hospitalName || '',
          branchCode: data.branchCode || '',
          branchName: data.branchName || '',
          medicalRecordNo: data.medicalRecordNo || '',
          hospitalizationTimes: data.hospitalizationTimes || '',
          recordTitle: data.recordTitle || '',
          recordTitleCode: data.recordTitleCode || '',
          recordTemplateName: data.recordTemplateName || '',
          recordText: data.recordText || '',
          recordStatus: data.recordStatus || 0,
          contentType: data.contentType || '',
          medicalNoteDate: data.medicalNoteDate || '',
          dateForPartition: data.dateForPartition || '',
          fromTable: data.fromTable || '',
          fromYyRecordId: data.fromYyRecordId || '',
          patientIdOld: data.patientIdOld || '',
          extendData1: data.extendData1 || '',
          extendData2: data.extendData2 || '',
          yyRecordId: data.yyRecordId || 0,
          yyRecordBatchId: data.yyRecordBatchId || '',
          yyRecordMd5: data.yyRecordMd5 || '',
          yyCollectionDatetime: data.yyCollectionDatetime || '',
          yyUploadTime: data.yyUploadTime || '',
          yyUploadStatus: data.yyUploadStatus || 0,
          yyBackfillTime: data.yyBackfillTime || '',
          yyBackfillStatus: data.yyBackfillStatus || 0,
          yyBatchTime: data.yyBatchTime || '',
          yyEtlTime: data.yyEtlTime || '',
          
          // 手术相关的其他字段
          surgeryDoctorName: data.surgeryDoctorName || '',
          surgeryDoctorCode: data.surgeryDoctorCode || '',
          anesthesiaDoctorName: data.anesthesiaDoctorName || '',
          anesthesiaDoctorCode: data.anesthesiaDoctorCode || '',
          anesthesiaStarttime: data.anesthesiaStarttime || '',
          anesthesiaEndtime: data.anesthesiaEndtime || '',
          bleedingVolum: data.bleedingVolum || '',
          volumeBlood: data.volumeBlood || '',
          operationTreatment: data.operationTreatment || '',
          surgeryTime: data.surgeryTime || ''
        }
      } catch (error) {
        console.error(`获取手术记录详情失败，visitSn: ${visitSn}`, error)
        
        // 如果是404错误，返回空数据而不是抛出异常
        if (error.response?.status === 404 || error.message?.includes('404')) {
          console.log(`手术记录不存在，visitSn: ${visitSn}，返回空数据`)
          return {
            recordSn: '',
            visitSn: visitSn,
            recordDatetime: '',
            recordUpdateDatetime: '',
            preOperativeDiagnosis: '',
            postOperationDiagnosis: '',
            surgeryName: '',
            anesthesiaMethod: '',
            surgeryStarttime: '',
            surgeryEndtime: '',
            surgeryProcess: ''
          }
        }
        
        throw new Error(error.message || '获取手术记录详情失败')
      } finally {
        // 请求完成后清除缓存
        this.detailPromiseCache.delete(cacheKey)
      }
    })()

    // 将Promise存入缓存
    this.detailPromiseCache.set(cacheKey, fetchPromise)

    return await fetchPromise
  }

  /**
   * 保存手术记录
   * @param {Object} data - 手术记录数据
   * @returns {Promise<Object>} 保存结果
   */
  static async save(data) {
    try {
      console.log('正在保存手术记录:', data)
      
      // 构建请求数据，映射表单字段到API字段
      const requestData = {
        visitSn: data.visitSn,
        recordSn: data.recordSn || '', // 更新时使用，新建时为空
        preOperativeDiagnosis: data.preOperativeDiagnosis || '',
        postOperationDiagnosis: data.postOperationDiagnosis || '',
        surgeryName: data.surgeryName || '',
        anesthesiaMethod: data.anesthesiaMethod || '',
        surgeryStarttime: data.surgeryStarttime || '',
        surgeryEndtime: data.surgeryEndtime || '',
        surgeryProcess: data.surgeryProcess || ''
      }
      
      const response = await request.post(surgeryRecordApi.save, requestData)
      console.log('手术记录保存成功')
      return response.data || response || {}
    } catch (error) {
      console.error('保存手术记录失败:', error)
      throw new Error(error.message || '保存手术记录失败')
    }
  }

  /**
   * 删除手术记录
   * @param {string|number} visitSn - 就诊唯一标识号
   * @returns {Promise<Object>} 删除结果
   */
  static async delete(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    try {
      console.log(`正在删除手术记录，visitSn: ${visitSn}`)
      const response = await request.delete(surgeryRecordApi.delete(visitSn))
      console.log('手术记录删除成功')
      return response.data || response || {}
    } catch (error) {
      console.error('删除手术记录失败:', error)
      throw new Error(error.message || '删除手术记录失败')
    }
  }
}

export default SurgeryRecordService
