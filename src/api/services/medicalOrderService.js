/**
 * 医嘱信息服务
 * 提供医嘱信息的增删改查功能
 */

import request from '@/utils/request'

/**
 * 医嘱信息服务类
 */
class MedicalOrderService {
  /**
   * 根据就诊号获取医嘱信息列表
   * @param {string} visitSn - 就诊号
   * @returns {Promise<Array>} 医嘱信息列表
   */
  async getList(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    try {

      const response = await request.get(`/b101/list/${visitSn}`)

      // 检查响应格式
      if (response.code !== 0) {
        throw new Error(response.msg || '获取医嘱信息列表失败')
      }

      const data = response.data || []
      return data
    } catch (error) {
      throw new Error(`获取医嘱信息列表失败: ${error.message}`)
    }
  }

  /**
   * 根据医嘱流水号获取医嘱信息详情
   * @param {string} orderSn - 医嘱流水号
   * @returns {Promise<Object>} 医嘱信息详情
   */
  async getDetail(orderSn) {
    if (!orderSn) {
      throw new Error('orderSn参数不能为空')
    }

    try {

      const response = await request.get(`/b101/detail/${orderSn}`)

      // 检查响应格式
      if (response.code !== 0) {
        throw new Error(response.msg || '获取医嘱信息详情失败')
      }

      const data = response.data || {}
      return data
    } catch (error) {
      throw new Error(`获取医嘱信息详情失败: ${error.message}`)
    }
  }

  /**
   * 新增医嘱信息
   * @param {Object} data - 医嘱信息数据
   * @param {Object} options - 选项参数
   * @param {boolean} options.force - 是否强制保存（绕过TNM分期检查）
   * @returns {Promise<Object>} 保存结果
   */
  async save(data, options = {}) {
    if (!data) {
      throw new Error('保存数据不能为空')
    }

    // 验证必填字段
    const requiredFields = ['visitSn', 'orderType', 'orderClassCode', 'orderClassName', 'orderItemName']
    for (const field of requiredFields) {
      if (!data[field]) {
        throw new Error(`${field}字段不能为空`)
      }
    }

    try {
      // 构建请求URL，如果是强制保存则添加参数
      let url = '/b101/add'
      if (options.force) {
        url += '?force=true'
      }

      const response = await request.post(url, data)

      // 检查响应格式
      if (response.code !== 0) {
        const error = new Error(response.msg || '保存医嘱信息失败')
        error.code = response.code
        error.data = response
        throw error
      }

      return response
    } catch (error) {
      // 如果是业务错误（有code字段），直接抛出原始错误
      if (error.code) {
        throw error
      }
      // 其他错误包装后抛出
      throw new Error(`保存医嘱信息失败: ${error.message}`)
    }
  }

  /**
   * 更新医嘱信息
   * @param {string} orderSn - 医嘱流水号
   * @param {Object} data - 更新数据
   * @param {Object} options - 选项参数
   * @param {boolean} options.force - 是否强制保存（绕过TNM分期检查）
   * @returns {Promise<Object>} 更新结果
   */
  async update(orderSn, data, options = {}) {
    if (!orderSn) {
      throw new Error('orderSn参数不能为空')
    }

    if (!data) {
      throw new Error('更新数据不能为空')
    }

    try {
      // 构建请求URL，如果是强制保存则添加参数
      let url = `/b101/update/${orderSn}`
      if (options.force) {
        url += '?force=true'
      }

      const response = await request.put(url, data)

      // 检查响应格式
      if (response.code !== 0) {
        throw new Error(response.msg || '更新医嘱信息失败')
      }

      return response
    } catch (error) {
      // 如果是业务错误（有code字段），直接抛出原始错误
      if (error.code) {
        throw error
      }
      // 其他错误包装后抛出
      throw new Error(`更新医嘱信息失败: ${error.message}`)
    }
  }

  /**
   * 删除医嘱信息
   * @param {string} orderSn - 医嘱流水号
   * @returns {Promise<Object>} 删除结果
   */
  async delete(orderSn) {
    if (!orderSn) {
      throw new Error('orderSn参数不能为空')
    }

    try {

      const response = await request.delete(`/b101/delete/${orderSn}`)

      // 检查响应格式
      if (response.code !== 0) {
        throw new Error(response.msg || '删除医嘱信息失败')
      }

      return response
    } catch (error) {
      throw new Error(`删除医嘱信息失败: ${error.message}`)
    }
  }

  /**
   * 获取医嘱信息数量
   * @param {string} visitSn - 就诊号
   * @returns {Promise<number>} 医嘱信息数量
   */
  async getCount(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    try {

      const list = await this.getList(visitSn)
      const count = Array.isArray(list) ? list.length : 0
      
      return count
    } catch (error) {
      throw new Error(`获取医嘱信息数量失败: ${error.message}`)
    }
  }
}

// 导出服务实例
export default new MedicalOrderService()
