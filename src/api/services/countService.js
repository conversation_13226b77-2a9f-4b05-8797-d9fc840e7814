/**
 * 统一计数接口服务
 * 用于获取各种业务数据的数量统计
 */

import request from '@/utils/request'

/**
 * 计数服务类
 */
export class CountService {
  // 防重复调用的Promise缓存
  static promiseCache = new Map()

  /**
   * 获取统一计数数据
   * @param {string} visitSn - 就诊序号
   * @returns {Promise<Object>} 计数数据对象
   */
  static async getUnifiedCount(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    const cacheKey = String(visitSn)

    // 如果正在请求相同visitSn的数据，返回同一个Promise避免重复请求
    if (this.promiseCache.has(cacheKey)) {
      console.log(`统一计数数据 ${visitSn} 请求进行中，等待现有请求完成...`)
      return await this.promiseCache.get(cacheKey)
    }

    // 创建新的请求Promise
    const fetchPromise = (async () => {
      try {
        console.log(`正在获取统一计数数据，visitSn: ${visitSn}`)
        
        const response = await request.get(`/count?visitSn=${visitSn}`)
        
        // 检查响应格式
        if (response.code !== 0) {
          throw new Error(response.msg || '获取统一计数数据失败')
        }

        const data = response.data || {}
        console.log('统一计数数据获取成功:', data)
        
        // 返回标准化的计数数据
        return {
          b061Count: data.b061Count || 0,  // 日常病程记录数量
          b161Count: data.b161Count || 0,  // 检查报告数量
          b162Count: data.b162Count || 0,  // 病理报告数量
          b163Count: data.b163Count || 0,  // 分子病理报告数量
          b171Count: data.b171Count || 0   // 检验报告数量
        }
      } catch (error) {
        console.error(`获取统一计数数据失败，visitSn: ${visitSn}`, error)
        
        // 如果是404错误，返回默认值而不是抛出异常
        if (error.response?.status === 404 || error.message?.includes('404')) {
          console.log(`统一计数数据不存在，visitSn: ${visitSn}，返回默认值`)
          return {
            b061Count: 0,
            b161Count: 0,
            b162Count: 0,
            b163Count: 0,
            b171Count: 0
          }
        }
        
        throw new Error(error.message || '获取统一计数数据失败')
      } finally {
        // 请求完成后清除缓存
        this.promiseCache.delete(cacheKey)
      }
    })()

    // 缓存Promise
    this.promiseCache.set(cacheKey, fetchPromise)
    
    return await fetchPromise
  }

  /**
   * 获取日常病程记录数量
   * @param {string} visitSn - 就诊序号
   * @returns {Promise<number>} 日常病程记录数量
   */
  static async getDailyProgressCount(visitSn) {
    const countData = await this.getUnifiedCount(visitSn)
    return countData.b061Count
  }

  /**
   * 获取检查报告数量
   * @param {string} visitSn - 就诊序号
   * @returns {Promise<number>} 检查报告数量
   */
  static async getExamReportCount(visitSn) {
    const countData = await this.getUnifiedCount(visitSn)
    return countData.b161Count
  }

  /**
   * 获取病理报告数量
   * @param {string} visitSn - 就诊序号
   * @returns {Promise<number>} 病理报告数量
   */
  static async getPathologyReportCount(visitSn) {
    const countData = await this.getUnifiedCount(visitSn)
    return countData.b162Count
  }

  /**
   * 获取分子病理报告数量
   * @param {string} visitSn - 就诊序号
   * @returns {Promise<number>} 分子病理报告数量
   */
  static async getMolecularPathologyCount(visitSn) {
    const countData = await this.getUnifiedCount(visitSn)
    return countData.b163Count
  }

  /**
   * 获取检验报告数量
   * @param {string} visitSn - 就诊序号
   * @returns {Promise<number>} 检验报告数量
   */
  static async getLabReportCount(visitSn) {
    const countData = await this.getUnifiedCount(visitSn)
    return countData.b171Count
  }
}

// 导出默认实例
export default CountService
