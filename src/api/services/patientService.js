/**
 * 患者相关API服务
 */

import request from '@/utils/request'
import { patientApi } from '@/api/endpoints'
import {getPatientAvatar} from "@/utils/patientUtils.js"
import { getDataSource } from '@/utils/urlParams'

/**
 * 患者服务类
 */
export class PatientService {
  // 防重复调用的Promise缓存
  static detailPromiseCache = new Map()

  /**
   * 根据科室代码获取患者卡片列表
   * @param {string|number} departmentCode - 科室代码
   * @param {string} source - 数据源参数 (ych: 测试医院, xrk: 向日葵)
   * @returns {Promise<Array>} 患者列表
   */
  static async getByDepartment(departmentCode, source = null) {
    try {
      // 如果没有传入source参数，从URL中获取
      const dataSource = source || getDataSource()

      const response = await request.get(patientApi.getByDepartment, {
        params: {
          departmentCode,
          source: dataSource
        }
      })
      
      // 数据映射：将API数据格式转换为前端所需格式
      if (response.data && Array.isArray(response.data)) {
        return response.data.map(item => ({

          // 基础信息
          medicalRecordNo: item.medicalRecordNo || '',
          hospitalizationCount: item.hospitalizationCount ,
          patientName: item.patientName || '',
          bedNo: item.bedNo || '',
          attendingPhysician: item.attendingPhysician || '',
          admissionDiagnosis: item.admissionDiagnosis || '',
          patientAge: item.patientAge || null,
          admissionTime: item.admissionTime || '',
          hospitalizationDays: item.hospitalizationDays || 0,
          patientAvatar: item.patientAvatar || null,

          // 新增字段支持
          visitSn: item.visitSn || null, // 重要：保留visitSn字段，使用null而不是空字符串
          dischargeTime: item.dischargeTime || null,
          ethnicity: item.ethnicity || '汉族',
          gender: item.gender , // 优先使用API返回的性别

          // 扩展信息（用于显示）
          id: item.medicalRecordNo || `patient_${Date.now()}_${Math.random()}`,
          name: item.patientName || '',
          age: item.patientAge || null,
          bed: item.bedNo || '',
          doctor: item.attendingPhysician || '',
          diagnosis: item.admissionDiagnosis || '',
          admissionDate: item.admissionTime || '',
          days: item.hospitalizationDays || 0,
          avatar: getPatientAvatar(item.patientAge, item.gender),

          // 添加格式化字段
          formattedGenderAge: `${item.gender || '未知'} ${item.patientAge || '未知'}岁`
        }))
      }

      return response.data
    } catch (error) {

      throw new Error(error.message || '获取科室患者卡片列表失败')
    }
  }
  
  /**
   * 获取患者列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Array>} 患者列表
   */
  static async getList(params = {}) {
    try {
      const response = await request.get(patientApi.getList, { params })
      return response.data || []
    } catch (error) {
      console.error('获取患者列表失败:', error)
      throw new Error(error.message || '获取患者列表失败')
    }
  }
  
  /**
   * 获取患者详情
   * @param {string|number} visitSn - 就诊唯一标识号
   * @returns {Promise<Object>} 患者详情
   */
  static async getDetail(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    const cacheKey = String(visitSn)

    // 如果正在请求相同visitSn的数据，返回同一个Promise避免重复请求
    if (this.detailPromiseCache.has(cacheKey)) {
      console.log(`患者详情 ${visitSn} 请求进行中，等待现有请求完成...`)
      return await this.detailPromiseCache.get(cacheKey)
    }

    // 创建新的请求Promise
    const fetchPromise = (async () => {
      try {
        console.log(`正在获取患者详情，visitSn: ${visitSn}`)
        const response = await request.get(patientApi.getByVisitSn(visitSn))
        console.log(`患者详情获取成功，visitSn: ${visitSn}`)
        return response.data || {}
      } catch (error) {
        console.error('获取患者详情失败:', error)
        throw new Error(error.message || '获取患者详情失败')
      } finally {
        // 清除Promise缓存，允许下次重新请求
        this.detailPromiseCache.delete(cacheKey)
      }
    })()

    // 缓存Promise
    this.detailPromiseCache.set(cacheKey, fetchPromise)

    return await fetchPromise
  }
  
  /**
   * 创建患者
   * @param {Object} data - 患者数据
   * @returns {Promise<Object>} 创建结果
   */
  static async create(data) {
    try {
      const response = await request.post(patientApi.create, data)
      return response.data || {}
    } catch (error) {
      console.error('创建患者失败:', error)
      throw new Error(error.message || '创建患者失败')
    }
  }
  
  /**
   * 更新患者
   * @param {string|number} id - 患者ID
   * @param {Object} data - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  static async update(id, data) {
    try {
      const response = await request.put(patientApi.update(id), data)
      return response.data || {}
    } catch (error) {
      console.error('更新患者失败:', error)
      throw new Error(error.message || '更新患者失败')
    }
  }
  
  /**
   * 删除患者
   * @param {string|number} id - 患者ID
   * @returns {Promise<Object>} 删除结果
   */
  static async delete(id) {
    try {
      const response = await request.delete(patientApi.delete(id))
      return response.data || {}
    } catch (error) {
      console.error('删除患者失败:', error)
      throw new Error(error.message || '删除患者失败')
    }
  }
  

}

// 导出默认实例
export default PatientService
