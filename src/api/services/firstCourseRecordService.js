/**
 * 首次病程记录相关API服务
 */

import request from '@/utils/request'
import { firstCourseRecordApi } from '@/api/endpoints'

/**
 * 首次病程记录服务类
 */
export class FirstCourseRecordService {
  // 防重复调用的Promise缓存
  static detailPromiseCache = new Map()

  /**
   * 获取首次病程记录详情
   * @param {string|number} visitSn - 就诊唯一标识号
   * @returns {Promise<Object>} 首次病程记录详情
   */
  static async getDetail(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    const cacheKey = String(visitSn)

    // 如果正在请求相同visitSn的数据，返回同一个Promise避免重复请求
    if (this.detailPromiseCache.has(cacheKey)) {
      console.log(`首次病程记录详情 ${visitSn} 请求进行中，等待现有请求完成...`)
      return await this.detailPromiseCache.get(cacheKey)
    }

    // 创建新的请求Promise
    const fetchPromise = (async () => {
      try {
        console.log(`正在获取首次病程记录详情，visitSn: ${visitSn}`)
        const response = await request.get(firstCourseRecordApi.getDetail(visitSn))
        console.log(`首次病程记录详情获取成功，visitSn: ${visitSn}`)
        
        // 处理响应数据，确保字段映射正确
        const data = response.data || response || {}
        
        // 映射API字段到表单字段
        const mappedData = {
          primaryDiagnosis: data.primaryDiagnosis || '',
          caseCharacter: data.caseCharacter || '',
          chiefComplaint: data.chiefComplaint || '',
          physicalExam: data.physicalExam || '',
          auxiliaryExam: data.auxiliaryExam || '',
          diagnosisBasis: data.diagnosisBasis || '',
          differentiatedDiagnosisDesc: data.differentiatedDiagnosisDesc || '',
          treatmentPlan: data.treatmentPlan || '',
          recordDatetime: data.recordDatetime || '',
          recordUpdateDatetime: data.recordUpdateDatetime || '',
          recordSn: data.recordSn || '',
          visitSn: data.visitSn || visitSn
        }
        
        return mappedData
      } catch (error) {
        console.error('获取首次病程记录详情失败:', error)
        // 如果是404错误，返回空数据而不是抛出错误
        if (error.response?.status === 404) {
          console.log('首次病程记录不存在，返回空数据')
          return {
            primaryDiagnosis: '',
            caseCharacter: '',
            chiefComplaint: '',
            physicalExam: '',
            auxiliaryExam: '',
            diagnosisBasis: '',
            differentiatedDiagnosisDesc: '',
            treatmentPlan: '',
            recordDatetime: '',
            recordUpdateDatetime: '',
            recordSn: '',
            visitSn: visitSn
          }
        }
        throw new Error(error.message || '获取首次病程记录详情失败')
      } finally {
        // 清除Promise缓存，允许下次重新请求
        this.detailPromiseCache.delete(cacheKey)
      }
    })()

    // 缓存Promise
    this.detailPromiseCache.set(cacheKey, fetchPromise)

    return await fetchPromise
  }

  /**
   * 保存首次病程记录
   * @param {Object} data - 首次病程记录数据
   * @returns {Promise<Object>} 保存结果
   */
  static async save(data) {
    try {
      console.log('正在保存首次病程记录:', data)
      
      // 构建请求数据，映射表单字段到API字段
      const requestData = {
        visitSn: data.visitSn,
        recordSn: data.recordSn || '', // 更新时使用，新建时为空
        primaryDiagnosis: data.primaryDiagnosis || '',
        caseCharacter: data.caseCharacter || '',
        chiefComplaint: data.chiefComplaint || '',
        physicalExam: data.physicalExam || '',
        auxiliaryExam: data.auxiliaryExam || '',
        diagnosisBasis: data.diagnosisBasis || '',
        differentiatedDiagnosisDesc: data.differentiatedDiagnosisDesc || '',
        treatmentPlan: data.treatmentPlan || ''
      }
      
      const response = await request.post(firstCourseRecordApi.save, requestData)
      console.log('首次病程记录保存成功')
      return response.data || response || {}
    } catch (error) {
      console.error('保存首次病程记录失败:', error)
      throw new Error(error.message || '保存首次病程记录失败')
    }
  }

  /**
   * 删除首次病程记录
   * @param {string|number} visitSn - 就诊唯一标识号
   * @returns {Promise<Object>} 删除结果
   */
  static async delete(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    try {
      console.log(`正在删除首次病程记录，visitSn: ${visitSn}`)
      const response = await request.delete(firstCourseRecordApi.delete(visitSn))
      console.log('首次病程记录删除成功')
      return response.data || response || {}
    } catch (error) {
      console.error('删除首次病程记录失败:', error)
      throw new Error(error.message || '删除首次病程记录失败')
    }
  }
}

export default FirstCourseRecordService
