/**
 * 第三方集成相关API服务
 */

import request from '@/utils/request'
import { thirdPartyIntegrationApi } from '@/api/endpoints'

/**
 * 第三方集成服务类
 */
export class ThirdPartyIntegrationService {
  /**
   * 获取患者历史数据（用于第三方系统集成）
   * @param {string} visitSn - 就诊流水号
   * @returns {Promise<Object>} 患者历史数据
   */
  static async getPatientHistory(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    try {
      console.log(`正在获取患者历史数据，visitSn: ${visitSn}`)
      const response = await request.get(thirdPartyIntegrationApi.getPatientHistory(visitSn))
      console.log(`患者历史数据获取成功，visitSn: ${visitSn}`)
      return response.data || {}
    } catch (error) {
      console.error('获取患者历史数据失败:', error)
      throw new Error(error.message || '获取患者历史数据失败')
    }
  }
}

export default ThirdPartyIntegrationService
