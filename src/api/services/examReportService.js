/**
 * 检查报告相关API服务
 */

import request from '@/utils/request'
import { examReportApi } from '@/api/endpoints'
import CountService from './countService'

/**
 * 检查报告服务类
 */
export class ExamReportService {
  // 防重复调用的Promise缓存
  static listPromiseCache = new Map()

  /**
   * 获取检查报告列表
   * @param {string|number} visitSn - 就诊唯一标识号
   * @returns {Promise<Array>} 检查报告列表
   */
  static async getList(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    const cacheKey = String(visitSn)

    // 如果正在请求相同visitSn的数据，返回同一个Promise避免重复请求
    if (this.listPromiseCache.has(cacheKey)) {
      console.log(`检查报告列表 ${visitSn} 请求进行中，等待现有请求完成...`)
      return await this.listPromiseCache.get(cacheKey)
    }

    // 创建新的请求Promise
    const fetchPromise = (async () => {
      try {
        console.log(`正在获取检查报告列表，visitSn: ${visitSn}`)
        const response = await request.get(examReportApi.getList(visitSn))
        console.log(`检查报告列表获取成功，visitSn: ${visitSn}`)
        
        // 处理响应数据
        const data = response.data || response || []
        
        // 确保返回数组格式，并标准化每个记录的数据
        const records = Array.isArray(data) ? data : []
        
        return records.map(record => ({
          // 基础信息
          visitSn: record.visitSn || visitSn,
          reportNo: record.reportNo || '',
          
          // 检查报告特有字段
          examItemType: record.examItemType || '',
          examSites: record.examSites || '',
          recordDatetime: record.recordDatetime || '',
          examDatetime: record.examDatetime || '',
          applyDatetime: record.applyDatetime || '',
          examItemName: record.examItemName || '',
          examDiagConclusion: record.examDiagConclusion || '',
          examDiagDescription: record.examDiagDescription || ''
        }))
      } catch (error) {
        console.error(`获取检查报告列表失败，visitSn: ${visitSn}`, error)
        
        // 如果是404错误，返回空数组而不是抛出异常
        if (error.response?.status === 404 || error.message?.includes('404')) {
          console.log(`检查报告不存在，visitSn: ${visitSn}，返回空数组`)
          return []
        }
        
        throw new Error(error.message || '获取检查报告列表失败')
      } finally {
        // 请求完成后清除缓存
        this.listPromiseCache.delete(cacheKey)
      }
    })()

    // 将Promise存入缓存
    this.listPromiseCache.set(cacheKey, fetchPromise)

    return await fetchPromise
  }

  /**
   * 保存检查报告
   * @param {Object} data - 检查报告数据
   * @returns {Promise<Object>} 保存结果
   */
  static async update(data) {
    try {
      console.log('正在保存检查报告:', data)
      
      // 构建请求数据，映射表单字段到API字段
      const requestData = {
        visitSn: data.visitSn,
        reportNo: data.reportNo || '',
        examItemType: data.examItemType || '',
        examSites: data.examSites || '',
        recordDatetime: data.recordDatetime || '',
        examDatetime: data.examDatetime || '',
        applyDatetime: data.applyDatetime || '',
        examItemName: data.examItemName || '',
        examDiagConclusion: data.examDiagConclusion || '',
        examDiagDescription: data.examDiagDescription || ''
      }
      
      const response = await request.put(examReportApi.update, requestData)
      console.log('检查报告保存成功')
      return response.data || response || {}
    } catch (error) {
      console.error('保存检查报告失败:', error)
      throw new Error(error.message || '保存检查报告失败')
    }
  }

  /**
   * 删除检查报告
   * @param {string|number} id - 报告ID
   * @returns {Promise<Object>} 删除结果
   */
  static async delete(id) {
    if (!id) {
      throw new Error('id参数不能为空')
    }

    try {
      console.log(`正在删除检查报告，id: ${id}`)
      const response = await request.delete(examReportApi.delete(id))
      console.log('检查报告删除成功')
      return response.data || response || {}
    } catch (error) {
      console.error('删除检查报告失败:', error)
      throw new Error(error.message || '删除检查报告失败')
    }
  }

  /**
   * 获取检查报告数量
   * @param {string|number} visitSn - 就诊唯一标识号
   * @returns {Promise<number>} 检查报告数量
   */
  static async getCount(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    try {
      console.log(`正在获取检查报告数量，visitSn: ${visitSn}`)
      const count = await CountService.getExamReportCount(visitSn)
      console.log(`检查报告数量获取成功，visitSn: ${visitSn}, count: ${count}`)
      return count
    } catch (error) {
      console.error(`获取检查报告数量失败，visitSn: ${visitSn}`, error)
      throw new Error(error.message || '获取检查报告数量失败')
    }
  }
}

export default ExamReportService
