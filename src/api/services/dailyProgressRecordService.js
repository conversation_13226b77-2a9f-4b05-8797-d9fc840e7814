/**
 * 日常病程记录相关API服务
 */

import request from '@/utils/request'
import { dailyProgressRecordApi } from '@/api/endpoints'
import CountService from './countService'

/**
 * 日常病程记录服务类
 */
export class DailyProgressRecordService {
  // 防重复调用的Promise缓存
  static listPromiseCache = new Map()
  static detailPromiseCache = new Map()
  static countPromiseCache = new Map()

  /**
   * 获取日常病程记录列表
   * @param {string|number} visitSn - 就诊唯一标识号
   * @returns {Promise<Array>} 日常病程记录列表
   */
  static async getList(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    const cacheKey = String(visitSn)

    // 如果正在请求相同visitSn的数据，返回同一个Promise避免重复请求
    if (this.listPromiseCache.has(cacheKey)) {
      return await this.listPromiseCache.get(cacheKey)
    }

    // 创建新的请求Promise
    const fetchPromise = (async () => {
      try {
        const response = await request.get(dailyProgressRecordApi.getList(visitSn))

        // 处理响应数据
        const data = response.data || response || []
        
        // 确保返回数组格式，并标准化每个记录的数据
        const records = Array.isArray(data) ? data : []
        
        return records.map(record => ({
          // 基础信息
          recordSn: record.recordSn || '',
          visitSn: record.visitSn || visitSn,
          recordDatetime: record.recordDatetime || '',
          recordUpdateDatetime: record.recordUpdateDatetime || '',
          
          // 日常病程记录特有字段
          recordContent: record.recordContent || '',
          
          // 其他可能的字段
          patientId: record.patientId || '',
          inpatientNo: record.inpatientNo || '',
          signatureDoctor: record.signatureDoctor || '',
          hospitalCode: record.hospitalCode || '',
          hospitalName: record.hospitalName || '',
          branchCode: record.branchCode || '',
          branchName: record.branchName || '',
          medicalRecordNo: record.medicalRecordNo || '',
          hospitalizationTimes: record.hospitalizationTimes || '',
          recordTitle: record.recordTitle || '',
          recordTitleCode: record.recordTitleCode || '',
          recordTemplateName: record.recordTemplateName || '',
          recordStatus: record.recordStatus || 0,
          contentType: record.contentType || '',
          dateForPartition: record.dateForPartition || '',
          fromTable: record.fromTable || '',
          fromYyRecordId: record.fromYyRecordId || '',
          patientIdOld: record.patientIdOld || '',
          extendData1: record.extendData1 || '',
          extendData2: record.extendData2 || '',
          yyRecordId: record.yyRecordId || 0,
          yyRecordBatchId: record.yyRecordBatchId || '',
          yyRecordMd5: record.yyRecordMd5 || '',
          yyCollectionDatetime: record.yyCollectionDatetime || '',
          yyUploadTime: record.yyUploadTime || '',
          yyUploadStatus: record.yyUploadStatus || 0,
          yyBackfillTime: record.yyBackfillTime || '',
          yyBackfillStatus: record.yyBackfillStatus || 0,
          yyBatchTime: record.yyBatchTime || '',
          yyEtlTime: record.yyEtlTime || ''
        }))
      } catch (error) {

        // 如果是404错误，返回空数组而不是抛出异常
        if (error.response?.status === 404 || error.message?.includes('404')) {
          return []
        }
        
        throw new Error(error.message || '获取日常病程记录列表失败')
      } finally {
        // 请求完成后清除缓存
        this.listPromiseCache.delete(cacheKey)
      }
    })()

    // 将Promise存入缓存
    this.listPromiseCache.set(cacheKey, fetchPromise)

    return await fetchPromise
  }

  /**
   * 获取日常病程记录详情
   * @param {string|number} recordSn - 记录流水号
   * @returns {Promise<Object>} 日常病程记录详情
   */
  static async getDetail(recordSn) {
    if (!recordSn) {
      throw new Error('recordSn参数不能为空')
    }

    const cacheKey = String(recordSn)

    // 如果正在请求相同recordSn的数据，返回同一个Promise避免重复请求
    if (this.detailPromiseCache.has(cacheKey)) {
      return await this.detailPromiseCache.get(cacheKey)
    }

    // 创建新的请求Promise
    const fetchPromise = (async () => {
      try {
        const response = await request.get(dailyProgressRecordApi.getDetail(recordSn))

        // 处理响应数据，确保字段映射正确
        const data = response.data || response || {}
        
        // 返回标准化的数据格式
        return {
          // 基础信息
          recordSn: data.recordSn || '',
          visitSn: data.visitSn || '',
          recordDatetime: data.recordDatetime || '',
          recordUpdateDatetime: data.recordUpdateDatetime || '',
          
          // 日常病程记录特有字段
          recordContent: data.recordContent || '',
          
          // 其他可能的字段
          patientId: data.patientId || '',
          inpatientNo: data.inpatientNo || '',
          signatureDoctor: data.signatureDoctor || '',
          hospitalCode: data.hospitalCode || '',
          hospitalName: data.hospitalName || '',
          branchCode: data.branchCode || '',
          branchName: data.branchName || '',
          medicalRecordNo: data.medicalRecordNo || '',
          hospitalizationTimes: data.hospitalizationTimes || '',
          recordTitle: data.recordTitle || '',
          recordTitleCode: data.recordTitleCode || '',
          recordTemplateName: data.recordTemplateName || '',
          recordStatus: data.recordStatus || 0,
          contentType: data.contentType || '',
          dateForPartition: data.dateForPartition || '',
          fromTable: data.fromTable || '',
          fromYyRecordId: data.fromYyRecordId || '',
          patientIdOld: data.patientIdOld || '',
          extendData1: data.extendData1 || '',
          extendData2: data.extendData2 || '',
          yyRecordId: data.yyRecordId || 0,
          yyRecordBatchId: data.yyRecordBatchId || '',
          yyRecordMd5: data.yyRecordMd5 || '',
          yyCollectionDatetime: data.yyCollectionDatetime || '',
          yyUploadTime: data.yyUploadTime || '',
          yyUploadStatus: data.yyUploadStatus || 0,
          yyBackfillTime: data.yyBackfillTime || '',
          yyBackfillStatus: data.yyBackfillStatus || 0,
          yyBatchTime: data.yyBatchTime || '',
          yyEtlTime: data.yyEtlTime || ''
        }
      } catch (error) {

        // 如果是404错误，返回空数据而不是抛出异常
        if (error.response?.status === 404 || error.message?.includes('404')) {
          return {
            recordSn: '',
            visitSn: '',
            recordDatetime: '',
            recordUpdateDatetime: '',
            recordContent: ''
          }
        }
        
        throw new Error(error.message || '获取日常病程记录详情失败')
      } finally {
        // 请求完成后清除缓存
        this.detailPromiseCache.delete(cacheKey)
      }
    })()

    // 将Promise存入缓存
    this.detailPromiseCache.set(cacheKey, fetchPromise)

    return await fetchPromise
  }

  /**
   * 保存日常病程记录
   * @param {Object} data - 日常病程记录数据
   * @returns {Promise<Object>} 保存结果
   */
  static async save(data) {
    try {

      // 构建请求数据，映射表单字段到API字段
      const requestData = {
        visitSn: data.visitSn,
        recordSn: data.recordSn || '', // 更新时使用，新建时为空
        recordContent: data.recordContent || ''
      }
      
      const response = await request.post(dailyProgressRecordApi.save, requestData)
      return response.data || response || {}
    } catch (error) {
      throw new Error(error.message || '保存日常病程记录失败')
    }
  }

  /**
   * 删除日常病程记录
   * @param {string|number} recordSn - 记录流水号
   * @returns {Promise<Object>} 删除结果
   */
  static async delete(recordSn) {
    if (!recordSn) {
      throw new Error('recordSn参数不能为空')
    }

    try {
      const response = await request.delete(dailyProgressRecordApi.delete(recordSn))
      return response.data || response || {}
    } catch (error) {
      throw new Error(error.message || '删除日常病程记录失败')
    }
  }

  /**
   * 获取日常病程记录数量
   * @param {string|number} visitSn - 就诊唯一标识号
   * @returns {Promise<number>} 记录数量
   */
  static async getCount(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    try {
      const count = await CountService.getDailyProgressCount(visitSn)
      return count
    } catch (error) {
      throw new Error(error.message || '获取日常病程记录数量失败')
    }
  }
}

export default DailyProgressRecordService
