/**
 * 病理报告相关API服务
 */

import request from '@/utils/request'
import { pathologyReportApi } from '@/api/endpoints'
import CountService from './countService'

/**
 * 病理报告服务类
 */
export class PathologyReportService {
  // 防重复调用的Promise缓存
  static listPromiseCache = new Map()

  /**
   * 获取病理报告列表
   * @param {string|number} visitSn - 就诊唯一标识号
   * @returns {Promise<Array>} 病理报告列表
   */
  static async getList(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    const cacheKey = String(visitSn)

    // 如果正在请求相同visitSn的数据，返回同一个Promise避免重复请求
    if (this.listPromiseCache.has(cacheKey)) {
      return await this.listPromiseCache.get(cacheKey)
    }

    // 创建新的请求Promise
    const fetchPromise = (async () => {
      try {
        const response = await request.get(pathologyReportApi.getList(visitSn))

        // 处理响应数据
        const data = response.data || response || []
        
        // 确保返回数组格式，并标准化每个记录的数据
        const records = Array.isArray(data) ? data : []
        
        return records.map(record => ({
          // 基础信息
          visitSn: record.visitSn || visitSn,
          id: record.reportNo || '',  // 使用 reportNo 作为 ID
          reportNo: record.reportNo || '',  // 保留原始字段名

          // 病理报告特有字段
          pathologyTestType: record.pathologyTestType || '',
          reportDatetime: record.reportDatetime || '',
          applyDatetime: record.applyDatetime || '',
          grossDescription: record.grossDescription || '',
          pathoDiagConclusion: record.pathoDiagConclusion || ''
        }))
      } catch (error) {

        // 如果是404错误，返回空数组而不是抛出异常
        if (error.response?.status === 404 || error.message?.includes('404')) {
          return []
        }
        
        throw new Error(error.message || '获取病理报告列表失败')
      } finally {
        // 清除缓存
        this.listPromiseCache.delete(cacheKey)
      }
    })()

    // 缓存Promise
    this.listPromiseCache.set(cacheKey, fetchPromise)
    
    return await fetchPromise
  }

  /**
   * 保存病理报告
   * @param {Object} data - 病理报告数据
   * @returns {Promise<Object>} 保存结果
   */
  static async update(data) {
    try {

      // 构建请求数据，映射表单字段到API字段
      const requestData = {
        reportNo: data.reportNo || '',
        pathologyTestType: data.pathologyTestType || '',
        reportDatetime: data.reportDatetime || '',
        applyDatetime: data.applyDatetime || '',
        grossDescription: data.grossDescription || '',
        pathoDiagConclusion: data.pathoDiagConclusion || ''
      }
      
      const response = await request.put(pathologyReportApi.update, requestData)
      return response.data || response || {}
    } catch (error) {
      throw new Error(error.message || '保存病理报告失败')
    }
  }

  /**
   * 删除病理报告
   * @param {string|number} id - 报告ID
   * @returns {Promise<Object>} 删除结果
   */
  static async delete(id) {
    if (!id) {
      throw new Error('id参数不能为空')
    }

    try {
      const response = await request.delete(pathologyReportApi.delete(id))
      return response.data || response || {}
    } catch (error) {
      throw new Error(error.message || '删除病理报告失败')
    }
  }

  /**
   * 获取病理报告数量
   * @param {string|number} visitSn - 就诊唯一标识号
   * @returns {Promise<number>} 病理报告数量
   */
  static async getCount(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    try {
      const count = await CountService.getPathologyReportCount(visitSn)
      return count
    } catch (error) {
      throw new Error(error.message || '获取病理报告数量失败')
    }
  }
}

export default PathologyReportService
