/**
 * 入院记录相关API服务
 */

import request from '@/utils/request'
import { admissionRecordApi } from '@/api/endpoints'

/**
 * 入院记录服务类
 */
export class AdmissionRecordService {
  // 防重复调用的Promise缓存
  static detailPromiseCache = new Map()

  /**
   * 获取入院记录详情
   * @param {string|number} visitSn - 就诊唯一标识号
   * @returns {Promise<Object>} 入院记录详情
   */
  static async getDetail(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    const cacheKey = String(visitSn)

    // 如果正在请求相同visitSn的数据，返回同一个Promise避免重复请求
    if (this.detailPromiseCache.has(cacheKey)) {
      return await this.detailPromiseCache.get(cacheKey)
    }

    // 创建新的请求Promise
    const fetchPromise = (async () => {
      try {
        const response = await request.get(admissionRecordApi.getDetail(visitSn))

        // 处理响应数据，确保字段映射正确
        const data = response.data || response || {}
        
        // 映射API字段到表单字段
        const mappedData = {
          chiefComplaint: data.chiefComplaint || '',
          currentMedhistory: data.currentMedhistory || '',
          pastMedhistory: data.pastMedhistory || '',
          personalMedhistory: data.personalMedhistory || '',
          menstrualHistory: data.menstrualHistory || '',
          marriageBirthHistory: data.marriageBirthHistory || '',
          familyHistory: data.familyHistory || '',
          auxiliaryExam: data.auxiliaryExam || '',
          physicalExam: data.physicalExam || '',
          primaryDiagnosis: data.primaryDiagnosis || '',
          recordDatetime: data.recordDatetime || '',
          recordUpdateDatetime: data.recordUpdateDatetime || '',
          recordSn: data.recordSn || '',
          visitSn: data.visitSn || visitSn
        }
        
        return mappedData
      } catch (error) {
        // 如果是404错误，返回空数据而不是抛出错误
        if (error.response?.status === 404) {
          return {
            chiefComplaint: '',
            currentMedhistory: '',
            pastMedhistory: '',
            personalMedhistory: '',
            menstrualHistory: '',
            marriageBirthHistory: '',
            familyHistory: '',
            auxiliaryExam: '',
            physicalExam: '',
            primaryDiagnosis: '',
            recordDatetime: '',
            recordUpdateDatetime: '',
            recordSn: '',
            visitSn: visitSn
          }
        }
        throw new Error(error.message || '获取入院记录详情失败')
      } finally {
        // 清除Promise缓存，允许下次重新请求
        this.detailPromiseCache.delete(cacheKey)
      }
    })()

    // 缓存Promise
    this.detailPromiseCache.set(cacheKey, fetchPromise)

    return await fetchPromise
  }

  /**
   * 保存入院记录
   * @param {Object} data - 入院记录数据
   * @returns {Promise<Object>} 保存结果
   */
  static async save(data) {
    try {

      // 构建请求数据，映射表单字段到API字段
      const requestData = {
        visitSn: data.visitSn,
        recordSn: data.recordSn || '', // 更新时使用，新建时为空
        chiefComplaint: data.chiefComplaint || '',
        currentMedhistory: data.currentMedhistory || '',
        pastMedhistory: data.pastMedhistory || '',
        personalMedhistory: data.personalMedhistory || '',
        menstrualHistory: data.menstrualHistory || '',
        marriageBirthHistory: data.marriageBirthHistory || '',
        familyHistory: data.familyHistory || '',
        auxiliaryExam: data.auxiliaryExam || '',
        physicalExam: data.physicalExam || '',
        primaryDiagnosis: data.primaryDiagnosis || ''
      }
      
      const response = await request.post(admissionRecordApi.save, requestData)
      return response.data || response || {}
    } catch (error) {
      throw new Error(error.message || '保存入院记录失败')
    }
  }

  /**
   * 删除入院记录
   * @param {string|number} visitSn - 就诊唯一标识号
   * @returns {Promise<Object>} 删除结果
   */
  static async delete(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    try {
      const response = await request.delete(admissionRecordApi.delete(visitSn))
      return response.data || response || {}
    } catch (error) {
      throw new Error(error.message || '删除入院记录失败')
    }
  }
}

export default AdmissionRecordService
