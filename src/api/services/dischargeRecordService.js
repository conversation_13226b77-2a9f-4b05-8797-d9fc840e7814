/**
 * 出院记录相关API服务
 */

import request from '@/utils/request'
import { dischargeRecordApi } from '@/api/endpoints'

/**
 * 出院记录服务类
 */
export class DischargeRecordService {
  // 防重复调用的Promise缓存
  static detailPromiseCache = new Map()

  /**
   * 获取出院记录详情
   * @param {string|number} visitSn - 就诊唯一标识号
   * @returns {Promise<Object>} 出院记录详情
   */
  static async getDetail(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    const cacheKey = String(visitSn)

    // 如果正在请求相同visitSn的数据，返回同一个Promise避免重复请求
    if (this.detailPromiseCache.has(cacheKey)) {
      console.log(`出院记录详情 ${visitSn} 请求进行中，等待现有请求完成...`)
      return await this.detailPromiseCache.get(cacheKey)
    }

    // 创建新的请求Promise
    const fetchPromise = (async () => {
      try {
        console.log(`正在获取出院记录详情，visitSn: ${visitSn}`)
        const response = await request.get(dischargeRecordApi.getDetail(visitSn))
        console.log(`出院记录详情获取成功，visitSn: ${visitSn}`)
        
        // 处理响应数据，确保字段映射正确
        const data = response.data || response || {}
        
        // 返回标准化的数据格式
        return {
          // 基础信息
          recordSn: data.recordSn || '',
          visitSn: data.visitSn || visitSn,
          recordDatetime: data.recordDatetime || '',
          recordUpdateDatetime: data.recordUpdateDatetime || '',
          
          // 出院记录特有字段
          admissionDiag: data.admissionDiag || '',
          dischargeDiag: data.dischargeDiag || '',
          admissionCondition: data.admissionCondition || '',
          treatmentInfo: data.treatmentInfo || '',
          dischargeCondition: data.dischargeCondition || '',
          dischargeOrder: data.dischargeOrder || '',
          
          // 其他可能的字段
          patientId: data.patientId || '',
          inpatientNo: data.inpatientNo || '',
          signatureDoctor: data.signatureDoctor || '',
          hospitalCode: data.hospitalCode || '',
          hospitalName: data.hospitalName || '',
          branchCode: data.branchCode || '',
          branchName: data.branchName || '',
          medicalRecordNo: data.medicalRecordNo || '',
          hospitalizationTimes: data.hospitalizationTimes || '',
          recordTitle: data.recordTitle || '',
          recordTitleCode: data.recordTitleCode || '',
          recordTemplateName: data.recordTemplateName || '',
          recordText: data.recordText || '',
          recordStatus: data.recordStatus || 0,
          contentType: data.contentType || '',
          medicalNoteDate: data.medicalNoteDate || '',
          dateForPartition: data.dateForPartition || '',
          fromTable: data.fromTable || '',
          fromYyRecordId: data.fromYyRecordId || '',
          patientIdOld: data.patientIdOld || '',
          extendData1: data.extendData1 || '',
          extendData2: data.extendData2 || '',
          yyRecordId: data.yyRecordId || 0,
          yyRecordBatchId: data.yyRecordBatchId || '',
          yyRecordMd5: data.yyRecordMd5 || '',
          yyCollectionDatetime: data.yyCollectionDatetime || '',
          yyUploadTime: data.yyUploadTime || '',
          yyUploadStatus: data.yyUploadStatus || 0,
          yyBackfillTime: data.yyBackfillTime || '',
          yyBackfillStatus: data.yyBackfillStatus || 0,
          yyBatchTime: data.yyBatchTime || '',
          yyEtlTime: data.yyEtlTime || '',
          // 出院记录特有的额外字段
          admissionDatetime: data.admissionDatetime || '',
          dischargeDatetime: data.dischargeDatetime || '',
          dischargeReason: data.dischargeReason || '',
          lengthOfStay: data.lengthOfStay || '',
          kpsScore: data.kpsScore || '',
          ecogScore: data.ecogScore || ''
        }
      } catch (error) {
        console.error(`获取出院记录详情失败，visitSn: ${visitSn}`, error)
        
        // 如果是404错误，返回空数据而不是抛出异常
        if (error.response?.status === 404 || error.message?.includes('404')) {
          console.log(`出院记录不存在，visitSn: ${visitSn}，返回空数据`)
          return {
            recordSn: '',
            visitSn: visitSn,
            recordDatetime: '',
            recordUpdateDatetime: '',
            admissionDiag: '',
            dischargeDiag: '',
            admissionCondition: '',
            treatmentInfo: '',
            dischargeCondition: '',
            dischargeOrder: ''
          }
        }
        
        throw new Error(error.message || '获取出院记录详情失败')
      } finally {
        // 请求完成后清除缓存
        this.detailPromiseCache.delete(cacheKey)
      }
    })()

    // 将Promise存入缓存
    this.detailPromiseCache.set(cacheKey, fetchPromise)

    return await fetchPromise
  }

  /**
   * 保存出院记录
   * @param {Object} data - 出院记录数据
   * @returns {Promise<Object>} 保存结果
   */
  static async save(data) {
    try {
      console.log('正在保存出院记录:', data)
      
      // 构建请求数据，映射表单字段到API字段
      const requestData = {
        visitSn: data.visitSn,
        recordSn: data.recordSn || '', // 更新时使用，新建时为空
        admissionDiag: data.admissionDiag || '',
        dischargeDiag: data.dischargeDiag || '',
        admissionCondition: data.admissionCondition || '',
        treatmentInfo: data.treatmentInfo || '',
        dischargeCondition: data.dischargeCondition || '',
        dischargeOrder: data.dischargeOrder || ''
      }
      
      const response = await request.post(dischargeRecordApi.save, requestData)
      console.log('出院记录保存成功')
      return response.data || response || {}
    } catch (error) {
      console.error('保存出院记录失败:', error)

      // 如果是业务错误（有code字段），直接抛出原始错误
      if (error.code) {
        throw error
      }

      // 其他错误包装后抛出
      throw new Error(error.message || '保存出院记录失败')
    }
  }

  /**
   * 删除出院记录
   * @param {string|number} visitSn - 就诊唯一标识号
   * @returns {Promise<Object>} 删除结果
   */
  static async delete(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    try {
      console.log(`正在删除出院记录，visitSn: ${visitSn}`)
      const response = await request.delete(dischargeRecordApi.delete(visitSn))
      console.log('出院记录删除成功')
      return response.data || response || {}
    } catch (error) {
      console.error('删除出院记录失败:', error)
      throw new Error(error.message || '删除出院记录失败')
    }
  }
}

export default DischargeRecordService
