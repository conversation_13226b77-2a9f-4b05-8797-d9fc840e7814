/**
 * 检验报告相关API服务
 */

import request from '@/utils/request'
import { labReportApi } from '@/api/endpoints'
import CountService from './countService'

/**
 * 检验报告服务类
 */
export class LabReportService {
  /**
   * 获取检验报告主表列表
   * @param {string} visitSn - 就诊序号
   * @returns {Promise<Array>} 检验报告主表列表
   */
  static async getMainList(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    try {
      console.log('正在获取检验报告主表列表，visitSn:', visitSn)
      
      const response = await request.get(labReportApi.getMainList(visitSn))
      
      // 检查响应格式
      if (response.code !== 0) {
        throw new Error(response.msg || '获取检验报告主表列表失败')
      }

      const data = response.data || []
      console.log('检验报告主表列表获取成功:', data)
      
      return data
    } catch (error) {
      console.error('获取检验报告主表列表失败:', error)
      throw new Error(error.message || '获取检验报告主表列表失败')
    }
  }

  /**
   * 获取检验报告明细列表
   * @param {string} labSn - 检验序号
   * @returns {Promise<Array>} 检验报告明细列表
   */
  static async getDetailList(labSn) {
    if (!labSn) {
      throw new Error('labSn参数不能为空')
    }

    try {
      console.log('正在获取检验报告明细列表，labSn:', labSn)
      
      const response = await request.get(labReportApi.getDetailList(labSn))
      
      // 检查响应格式
      if (response.code !== 0) {
        throw new Error(response.msg || '获取检验报告明细列表失败')
      }

      const data = response.data || []
      console.log('检验报告明细列表获取成功:', data)
      
      return data
    } catch (error) {
      console.error('获取检验报告明细列表失败:', error)
      throw new Error(error.message || '获取检验报告明细列表失败')
    }
  }

  /**
   * 获取检验报告数量
   * @param {string} visitSn - 就诊序号
   * @returns {Promise<number>} 检验报告数量
   */
  static async getCount(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    try {
      console.log(`正在获取检验报告数量，visitSn: ${visitSn}`)
      const count = await CountService.getLabReportCount(visitSn)
      console.log(`检验报告数量获取成功，visitSn: ${visitSn}, count: ${count}`)
      return count
    } catch (error) {
      console.error(`获取检验报告数量失败，visitSn: ${visitSn}`, error)
      throw new Error(error.message || '获取检验报告数量失败')
    }
  }
}

// 导出默认实例
export default LabReportService
