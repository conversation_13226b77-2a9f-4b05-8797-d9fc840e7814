/**
 * 有创操作记录相关API服务
 */

import request from '@/utils/request'
import { invasiveProcedureApi } from '@/api/endpoints'

/**
 * 有创操作记录服务类
 */
export class InvasiveProcedureService {
  // 防重复调用的Promise缓存
  static detailPromiseCache = new Map()

  /**
   * 获取有创操作记录详情
   * @param {string|number} visitSn - 就诊唯一标识号
   * @returns {Promise<Object>} 有创操作记录详情
   */
  static async getDetail(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    const cacheKey = String(visitSn)

    // 如果正在请求相同visitSn的数据，返回同一个Promise避免重复请求
    if (this.detailPromiseCache.has(cacheKey)) {
      console.log(`有创操作记录详情 ${visitSn} 请求进行中，等待现有请求完成...`)
      return await this.detailPromiseCache.get(cacheKey)
    }

    // 创建新的请求Promise
    const fetchPromise = (async () => {
      try {
        console.log(`正在获取有创操作记录详情，visitSn: ${visitSn}`)
        const response = await request.get(invasiveProcedureApi.getDetail(visitSn))
        console.log(`有创操作记录详情获取成功，visitSn: ${visitSn}`)
        
        // 处理响应数据，确保字段映射正确
        const data = response.data || response || {}
        
        // 返回标准化的数据格式
        return {
          // 基础信息
          recordSn: data.recordSn || '',
          visitSn: data.visitSn || visitSn,
          recordDatetime: data.recordDatetime || '',
          recordUpdateDatetime: data.recordUpdateDatetime || '',
          
          // 有创操作记录特有字段
          perfromDatetime: data.perfromDatetime || '',
          procedureName: data.procedureName || '',
          recordContent: data.recordContent || '',
          mattersNeedingAttention: data.mattersNeedingAttention || '',
          
          // 其他可能的字段
          patientId: data.patientId || '',
          inpatientNo: data.inpatientNo || '',
          signatureDoctor: data.signatureDoctor || '',
          hospitalCode: data.hospitalCode || '',
          hospitalName: data.hospitalName || '',
          branchCode: data.branchCode || '',
          branchName: data.branchName || '',
          medicalRecordNo: data.medicalRecordNo || '',
          hospitalizationTimes: data.hospitalizationTimes || '',
          recordTitle: data.recordTitle || '',
          recordTitleCode: data.recordTitleCode || '',
          recordTemplateName: data.recordTemplateName || '',
          recordText: data.recordText || '',
          recordStatus: data.recordStatus || 0,
          contentType: data.contentType || '',
          medicalNoteDate: data.medicalNoteDate || '',
          dateForPartition: data.dateForPartition || '',
          fromTable: data.fromTable || '',
          fromYyRecordId: data.fromYyRecordId || '',
          patientIdOld: data.patientIdOld || '',
          extendData1: data.extendData1 || '',
          extendData2: data.extendData2 || '',
          yyRecordId: data.yyRecordId || 0,
          yyRecordBatchId: data.yyRecordBatchId || '',
          yyRecordMd5: data.yyRecordMd5 || '',
          yyCollectionDatetime: data.yyCollectionDatetime || '',
          yyUploadTime: data.yyUploadTime || '',
          yyUploadStatus: data.yyUploadStatus || 0,
          yyBackfillTime: data.yyBackfillTime || '',
          yyBackfillStatus: data.yyBackfillStatus || 0,
          yyBatchTime: data.yyBatchTime || '',
          yyEtlTime: data.yyEtlTime || ''
        }
      } catch (error) {
        console.error(`获取有创操作记录详情失败，visitSn: ${visitSn}`, error)
        
        // 如果是404错误，返回空数据而不是抛出异常
        if (error.response?.status === 404 || error.message?.includes('404')) {
          console.log(`有创操作记录不存在，visitSn: ${visitSn}，返回空数据`)
          return {
            recordSn: '',
            visitSn: visitSn,
            recordDatetime: '',
            recordUpdateDatetime: '',
            perfromDatetime: '',
            procedureName: '',
            recordContent: '',
            mattersNeedingAttention: ''
          }
        }
        
        throw new Error(error.message || '获取有创操作记录详情失败')
      } finally {
        // 请求完成后清除缓存
        this.detailPromiseCache.delete(cacheKey)
      }
    })()

    // 将Promise存入缓存
    this.detailPromiseCache.set(cacheKey, fetchPromise)

    return await fetchPromise
  }

  /**
   * 保存有创操作记录
   * @param {Object} data - 有创操作记录数据
   * @returns {Promise<Object>} 保存结果
   */
  static async save(data) {
    try {
      console.log('正在保存有创操作记录:', data)
      
      // 构建请求数据，映射表单字段到API字段
      const requestData = {
        visitSn: data.visitSn,
        recordSn: data.recordSn || '', // 更新时使用，新建时为空
        perfromDatetime: data.perfromDatetime || '',
        procedureName: data.procedureName || '',
        recordContent: data.recordContent || '',
        mattersNeedingAttention: data.mattersNeedingAttention || ''
      }
      
      const response = await request.post(invasiveProcedureApi.save, requestData)
      console.log('有创操作记录保存成功')
      return response.data || response || {}
    } catch (error) {
      console.error('保存有创操作记录失败:', error)
      throw new Error(error.message || '保存有创操作记录失败')
    }
  }

  /**
   * 删除有创操作记录
   * @param {string|number} visitSn - 就诊唯一标识号
   * @returns {Promise<Object>} 删除结果
   */
  static async delete(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    try {
      console.log(`正在删除有创操作记录，visitSn: ${visitSn}`)
      const response = await request.delete(invasiveProcedureApi.delete(visitSn))
      console.log('有创操作记录删除成功')
      return response.data || response || {}
    } catch (error) {
      console.error('删除有创操作记录失败:', error)
      throw new Error(error.message || '删除有创操作记录失败')
    }
  }
}

export default InvasiveProcedureService
