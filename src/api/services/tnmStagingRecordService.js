/**
 * TNM分期记录相关API服务
 */

import request from '@/utils/request'
import { tnmStagingRecordApi } from '@/api/endpoints'

/**
 * TNM分期记录服务类
 */
export class TnmStagingRecordService {
  // 防重复调用的Promise缓存
  static detailPromiseCache = new Map()

  /**
   * 获取TNM分期记录详情
   * @param {string|number} visitSn - 就诊唯一标识号
   * @returns {Promise<Object>} TNM分期记录详情
   */
  static async getDetail(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    const cacheKey = String(visitSn)

    // 如果正在请求相同visitSn的数据，返回同一个Promise避免重复请求
    if (this.detailPromiseCache.has(cacheKey)) {
      console.log(`TNM分期记录详情 ${visitSn} 请求进行中，等待现有请求完成...`)
      return await this.detailPromiseCache.get(cacheKey)
    }

    // 创建新的请求Promise
    const fetchPromise = (async () => {
      try {
        console.log(`正在获取TNM分期记录详情，visitSn: ${visitSn}`)
        const response = await request.get(tnmStagingRecordApi.getDetail(visitSn))
        console.log(`TNM分期记录详情获取成功，visitSn: ${visitSn}`)
        
        // 处理响应数据，确保字段映射正确
        const data = response.data || response || {}
        
        // 返回标准化的数据结构
        return {
          recordSn: data.recordSn || '',
          visitSn: data.visitSn || visitSn,
          recordDatetime: data.recordDatetime || '',
          recordUpdateDatetime: data.recordUpdateDatetime || '',
          diagnosisItemCode: data.diagnosisItemCode || '',
          tnmTStaging: data.tnmTStaging || '',
          tnmNStaging: data.tnmNStaging || '',
          tnmMStaging: data.tnmMStaging || '',
          figoStagingCervicalCancer: data.figoStagingCervicalCancer || '',
          cnlcStagingHepatocellular: data.cnlcStagingHepatocellular || '',
          annArborStagingLymphoma: data.annArborStagingLymphoma || '',
          stagingDatetime: data.stagingDatetime || ''
        }
      } catch (error) {
        console.error(`获取TNM分期记录详情失败，visitSn: ${visitSn}`, error)
        
        // 如果是404错误，返回空数据而不是抛出异常
        if (error.response?.status === 404 || error.message?.includes('404')) {
          console.log(`TNM分期记录不存在，visitSn: ${visitSn}，返回空数据`)
          return {
            recordSn: '',
            visitSn: visitSn,
            recordDatetime: '',
            recordUpdateDatetime: '',
            diagnosisItemCode: '',
            tnmTStaging: '',
            tnmNStaging: '',
            tnmMStaging: '',
            figoStagingCervicalCancer: '',
            cnlcStagingHepatocellular: '',
            annArborStagingLymphoma: '',
            stagingDatetime: ''
          }
        }
        
        throw new Error(error.message || '获取TNM分期记录详情失败')
      } finally {
        // 请求完成后清除缓存
        this.detailPromiseCache.delete(cacheKey)
      }
    })()

    // 将Promise存入缓存
    this.detailPromiseCache.set(cacheKey, fetchPromise)

    return await fetchPromise
  }

  /**
   * 保存TNM分期记录
   * @param {Object} data - TNM分期记录数据
   * @returns {Promise<Object>} 保存结果
   */
  static async save(data) {
    try {
      console.log('正在保存TNM分期记录:', data)
      
      // 构建请求数据，映射表单字段到API字段
      const requestData = {
        visitSn: data.visitSn,
        recordSn: data.recordSn || '', // 更新时使用，新建时为空
        diagnosisItemCode: data.diagnosisItemCode || '',
        tnmTStaging: data.tnmTStaging || '',
        tnmNStaging: data.tnmNStaging || '',
        tnmMStaging: data.tnmMStaging || '',
        figoStagingCervicalCancer: data.figoStagingCervicalCancer || '',
        cnlcStagingHepatocellular: data.cnlcStagingHepatocellular || '',
        annArborStagingLymphoma: data.annArborStagingLymphoma || '',
        stagingDatetime: data.stagingDatetime || ''
      }
      
      const response = await request.post(tnmStagingRecordApi.save, requestData)
      console.log('TNM分期记录保存成功')
      return response.data || response || {}
    } catch (error) {
      console.error('保存TNM分期记录失败:', error)
      throw new Error(error.message || '保存TNM分期记录失败')
    }
  }

  /**
   * 删除TNM分期记录
   * @param {string|number} visitSn - 就诊唯一标识号
   * @returns {Promise<Object>} 删除结果
   */
  static async delete(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    try {
      console.log(`正在删除TNM分期记录，visitSn: ${visitSn}`)
      const response = await request.delete(tnmStagingRecordApi.delete(visitSn))
      console.log('TNM分期记录删除成功')
      return response.data || response || {}
    } catch (error) {
      console.error('删除TNM分期记录失败:', error)
      throw new Error(error.message || '删除TNM分期记录失败')
    }
  }
}

export default TnmStagingRecordService
