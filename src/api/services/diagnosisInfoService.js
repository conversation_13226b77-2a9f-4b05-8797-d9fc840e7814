/**
 * 诊断信息相关API服务
 */

import request from '@/utils/request'
import { diagnosisInfoApi } from '@/api/endpoints'

/**
 * 诊断信息服务类
 */
export class DiagnosisInfoService {
  /**
   * 获取诊断信息列表
   * @param {string} visitSn - 就诊序号
   * @param {string} diagType - 诊断类型（可选）
   * @returns {Promise<Array>} 诊断信息列表
   */
  static async getList(visitSn, diagType = null) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    try {
      console.log('正在获取诊断信息列表，visitSn:', visitSn, 'diagType:', diagType)

      // 构建查询参数
      const params = { visitSn }
      if (diagType) {
        params.diagType = diagType
      }

      const response = await request.get(diagnosisInfoApi.getList, { params })

      // 检查响应格式
      if (response.code !== 0) {
        throw new Error(response.msg || '获取诊断信息列表失败')
      }

      const data = response.data || []
      console.log('诊断信息列表获取成功:', data)

      return data
    } catch (error) {
      console.error('获取诊断信息列表失败:', error)
      throw new Error(error.message || '获取诊断信息列表失败')
    }
  }

  /**
   * 获取诊断信息列表（按诊断类型）
   * @param {string} visitSn - 就诊序号
   * @param {string} diagType - 诊断类型
   * @returns {Promise<Array>} 诊断信息列表
   */
  static async getListByType(visitSn, diagType) {
    return this.getList(visitSn, diagType)
  }

  /**
   * 获取诊断信息详情
   * @param {string} diagId - 诊断ID
   * @param {string} diagSource - 诊断数据来源
   * @returns {Promise<Object>} 诊断信息详情
   */
  static async getDetail(diagId, diagSource) {
    if (!diagId || !diagSource) {
      throw new Error('diagId和diagSource参数不能为空')
    }

    try {
      console.log('正在获取诊断信息详情，diagId:', diagId, 'diagSource:', diagSource)
      
      const response = await request.get(diagnosisInfoApi.getDetail(diagId, diagSource))
      
      // 检查响应格式
      if (response.code !== 0) {
        throw new Error(response.msg || '获取诊断信息详情失败')
      }

      const data = response.data || {}
      console.log('诊断信息详情获取成功:', data)
      
      return data
    } catch (error) {
      console.error('获取诊断信息详情失败:', error)
      throw new Error(error.message || '获取诊断信息详情失败')
    }
  }

  /**
   * 保存诊断信息
   * @param {Object} data - 诊断信息数据
   * @returns {Promise<Object>} 保存结果
   */
  static async save(data) {
    if (!data) {
      throw new Error('保存数据不能为空')
    }

    try {
      console.log('正在保存诊断信息:', data)
      
      const response = await request.post(diagnosisInfoApi.save, data)
      
      // 检查响应格式
      if (response.code !== 0) {
        throw new Error(response.msg || '保存诊断信息失败')
      }

      const result = response.data || {}
      console.log('诊断信息保存成功:', result)
      
      return result
    } catch (error) {
      console.error('保存诊断信息失败:', error)
      throw new Error(error.message || '保存诊断信息失败')
    }
  }

  /**
   * 更新诊断信息
   * @param {string} diagId - 诊断ID
   * @param {string} diagSource - 诊断数据来源
   * @param {Object} data - 诊断信息数据
   * @returns {Promise<boolean>} 更新结果
   */
  static async update(diagId, diagSource, data) {
    if (!diagId || !diagSource || !data) {
      throw new Error('diagId、diagSource和data参数不能为空')
    }

    try {
      console.log('正在更新诊断信息，diagId:', diagId, 'diagSource:', diagSource, 'data:', data)
      
      const response = await request.put(diagnosisInfoApi.update(diagId, diagSource), data)
      
      // 检查响应格式
      if (response.code !== 0) {
        throw new Error(response.msg || '更新诊断信息失败')
      }

      const result = response.data
      console.log('诊断信息更新成功:', result)
      
      return result
    } catch (error) {
      console.error('更新诊断信息失败:', error)
      throw new Error(error.message || '更新诊断信息失败')
    }
  }

  /**
   * 删除诊断信息
   * @param {string} diagId - 诊断ID
   * @param {string} diagSource - 诊断数据来源
   * @returns {Promise<boolean>} 删除结果
   */
  static async delete(diagId, diagSource) {
    if (!diagId || !diagSource) {
      throw new Error('diagId和diagSource参数不能为空')
    }

    try {
      console.log('正在删除诊断信息，diagId:', diagId, 'diagSource:', diagSource)
      
      const response = await request.delete(diagnosisInfoApi.delete(diagId, diagSource))
      
      // 检查响应格式
      if (response.code !== 0) {
        throw new Error(response.msg || '删除诊断信息失败')
      }

      const result = response.data
      console.log('诊断信息删除成功:', result)
      
      return result
    } catch (error) {
      console.error('删除诊断信息失败:', error)
      throw new Error(error.message || '删除诊断信息失败')
    }
  }

  /**
   * 获取诊断信息数量
   * @param {string} visitSn - 就诊序号
   * @returns {Promise<number>} 诊断信息数量
   */
  static async getCount(visitSn) {
    if (!visitSn) {
      throw new Error('visitSn参数不能为空')
    }

    try {
      console.log('正在获取诊断信息数量，visitSn:', visitSn)

      // 通过获取列表长度来计算数量，不再调用单独的计数接口
      const list = await this.getList(visitSn)
      const count = Array.isArray(list) ? list.length : 0

      console.log('诊断信息数量获取成功:', count)
      return count
    } catch (error) {
      console.error('获取诊断信息数量失败:', error)
      throw new Error(error.message || '获取诊断信息数量失败')
    }
  }
}

// 导出默认实例
export default DiagnosisInfoService
