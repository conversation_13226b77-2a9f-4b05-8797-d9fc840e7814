/**
 * 科室相关API服务
 */

import request from '@/utils/request'
import { departmentApi } from '@/api/endpoints'

/**
 * 科室服务类
 */
export class DepartmentService {
  /**
   * 获取科室统计数据
   * @param {string} source - 数据源参数 (ych: 测试医院, xrk: 向日葵)
   * @returns {Promise<Array>} 科室统计数据列表
   */
  static async getStats(source = 'ych') {
    try {
      const response = await request.get(departmentApi.getStats, {
        params: { source }
      })

      // 数据映射：将API数据格式转换为前端所需格式
      if (response.data && Array.isArray(response.data)) {
        return response.data.map((item, index) => ({
          id: index + 1, // 为每个科室添加唯一ID
          name: item.departmentName, // departmentName → name
          code: item.departmentCode || item.departmentName || `dept_${index + 1}`, // 科室代码
          patientCount: item.patientCount || 0 // 确保patientCount有默认值
        }))
      }

      return []
    } catch (error) {
      console.error('获取科室统计数据失败:', error)
      throw new Error(error.message || '获取科室统计数据失败')
    }
  }
  
  /**
   * 获取科室列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Array>} 科室列表
   */
  static async getList(params = {}) {
    try {
      const response = await request.get(departmentApi.getList, { params })
      return response.data || []
    } catch (error) {
      console.error('获取科室列表失败:', error)
      throw new Error(error.message || '获取科室列表失败')
    }
  }
  
  /**
   * 获取科室详情
   * @param {string|number} id - 科室ID
   * @returns {Promise<Object>} 科室详情
   */
  static async getDetail(id) {
    try {
      const response = await request.get(departmentApi.getDetail(id))
      return response.data || {}
    } catch (error) {
      console.error('获取科室详情失败:', error)
      throw new Error(error.message || '获取科室详情失败')
    }
  }
  
  /**
   * 创建科室
   * @param {Object} data - 科室数据
   * @returns {Promise<Object>} 创建结果
   */
  static async create(data) {
    try {
      const response = await request.post(departmentApi.create, data)
      return response.data || {}
    } catch (error) {
      console.error('创建科室失败:', error)
      throw new Error(error.message || '创建科室失败')
    }
  }
  
  /**
   * 更新科室
   * @param {string|number} id - 科室ID
   * @param {Object} data - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  static async update(id, data) {
    try {
      const response = await request.put(departmentApi.update(id), data)
      return response.data || {}
    } catch (error) {
      console.error('更新科室失败:', error)
      throw new Error(error.message || '更新科室失败')
    }
  }
  
  /**
   * 删除科室
   * @param {string|number} id - 科室ID
   * @returns {Promise<Object>} 删除结果
   */
  static async delete(id) {
    try {
      const response = await request.delete(departmentApi.delete(id))
      return response.data || {}
    } catch (error) {
      console.error('删除科室失败:', error)
      throw new Error(error.message || '删除科室失败')
    }
  }
}

// 导出默认实例
export default DepartmentService
