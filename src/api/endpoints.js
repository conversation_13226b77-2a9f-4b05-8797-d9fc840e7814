/**
 * API端点定义
 * 集中管理所有API接口地址
 */

/**
 * 科室相关API
 */
export const departmentApi = {
  // 获取科室统计数据
  getStats: '/b032/stats/by-department',
  
  // 获取科室列表
  getList: '/b032/departments',
  
  // 获取科室详情
  getDetail: (id) => `/b032/departments/${id}`,


}

/**
 * 患者相关API
 */
export const patientApi = {
  // 获取患者列表
  getList: '/b032/patients',

  // 获取患者详情（通过visitSn）
  getByVisitSn: (visitSn) => `/b032/patient-cards/getByVisitSn?visitSn=${visitSn}`,

  // 按科室获取患者卡片列表
  getByDepartment: '/b032/patient-cards',

  // 创建患者
  create: '/b032/patients',

  // 更新患者
  update: (id) => `/b032/patients/${id}`,

  // 删除患者
  delete: (id) => `/b032/patients/${id}`
}




/**
 * 入院记录相关API
 */
export const admissionRecordApi = {
  // 获取入院记录详情
  getDetail: (visitSn) => `/b051/detail/${visitSn}`,

  // 保存入院记录
  save: '/b051/save',

  // 删除入院记录
  delete: (visitSn) => `/b051/delete/${visitSn}`
}

/**
 * 首次病程记录相关API
 */
export const firstCourseRecordApi = {
  // 获取首次病程记录详情
  getDetail: (visitSn) => `/b0611/detail/${visitSn}`,

  // 保存首次病程记录
  save: '/b0611/save',

  // 删除首次病程记录
  delete: (visitSn) => `/b0611/delete/${visitSn}`
}

/**
 * 会诊记录相关API
 */
export const consultationRecordApi = {
  // 获取会诊记录详情
  getDetail: (visitSn) => `/b0612/detail/${visitSn}`,

  // 保存会诊记录
  save: '/b0612/save',

  // 删除会诊记录
  delete: (visitSn) => `/b0612/delete/${visitSn}`
}

/**
 * 术前讨论相关API
 */
export const preoperativeDiscussionApi = {
  // 获取术前讨论详情
  getDetail: (visitSn) => `/b0613/detail/${visitSn}`,

  // 保存术前讨论
  save: '/b0613/save',

  // 删除术前讨论
  delete: (visitSn) => `/b0613/delete/${visitSn}`
}

/**
 * 术前小结相关API
 */
export const preoperativeSummaryApi = {
  // 获取术前小结详情
  getDetail: (visitSn) => `/b0614/detail/${visitSn}`,

  // 保存术前小结
  save: '/b0614/save',

  // 删除术前小结
  delete: (visitSn) => `/b0614/delete/${visitSn}`
}

/**
 * 有创操作记录相关API
 */
export const invasiveProcedureApi = {
  // 获取有创操作记录详情
  getDetail: (visitSn) => `/b0615/detail/${visitSn}`,

  // 保存有创操作记录
  save: '/b0615/save',

  // 删除有创操作记录
  delete: (visitSn) => `/b0615/delete/${visitSn}`
}

/**
 * 手术记录相关API
 */
export const surgeryRecordApi = {
  // 获取手术记录详情
  getDetail: (visitSn) => `/b0616/detail/${visitSn}`,

  // 保存手术记录
  save: '/b0616/save',

  // 删除手术记录
  delete: (visitSn) => `/b0616/delete/${visitSn}`
}

/**
 * 术后首次病程记录相关API
 */
export const postoperativeFirstCourseRecordApi = {
  // 获取术后首次病程记录详情
  getDetail: (visitSn) => `/b0617/detail/${visitSn}`,

  // 保存术后首次病程记录
  save: '/b0617/save',

  // 删除术后首次病程记录
  delete: (visitSn) => `/b0617/delete/${visitSn}`
}

/**
 * 出院记录相关API
 */
export const dischargeRecordApi = {
  // 获取出院记录详情
  getDetail: (visitSn) => `/b071/detail/${visitSn}`,

  // 保存出院记录
  save: '/b071/save',

  // 删除出院记录
  delete: (visitSn) => `/b071/delete/${visitSn}`
}

/**
 * 日常病程记录相关API
 */
export const dailyProgressRecordApi = {
  // 获取日常病程记录列表
  getList: (visitSn) => `/b061/list?visitSn=${visitSn}`,

  // 获取日常病程记录详情
  getDetail: (recordSn) => `/b061/detail/${recordSn}`,

  // 保存日常病程记录
  save: '/b061/save',

  // 删除日常病程记录
  delete: (recordSn) => `/b061/delete/${recordSn}`
}

/**
 * 检查报告相关API
 */
export const examReportApi = {
  // 获取检查报告列表
  getList: (visitSn) => `/b161/list/${visitSn}`,

  // 保存检查报告
  update: '/b161/update',

  // 删除检查报告
  delete: (id) => `/b161/delete/${id}`
}

/**
 * TNM分期记录相关API
 */
export const tnmStagingRecordApi = {
  // 获取TNM分期记录详情
  getDetail: (visitSn) => `/tnm-t-staging/detail/${visitSn}`,

  // 保存TNM分期记录
  save: '/tnm-t-staging/save',

  // 删除TNM分期记录
  delete: (visitSn) => `/tnm-t-staging/delete/${visitSn}`
}

/**
 * 字典数据相关API
 */
export const dictionaryApi = {
  // 根据字典类型获取字典数据
  getByType: (dictType) => `/system-dict-data/${dictType}`,

  // 分页获取字典数据
  getPage: '/system-dict-data/page'
}

/**
 * 诊断信息相关API
 */
export const diagnosisInfoApi = {
  // 获取诊断信息列表（支持按诊断类型过滤）
  getList: '/b023/list',

  // 获取诊断信息详情
  getDetail: (diagId, diagSource) => `/b023/detail/${diagId}/${diagSource}`,

  // 保存诊断信息
  save: '/b023/add',

  // 更新诊断信息
  update: (diagId, diagSource) => `/b023/update/${diagId}/${diagSource}`,

  // 删除诊断信息
  delete: (diagId, diagSource) => `/b023/delete/${diagId}/${diagSource}`
}

/**
 * 检验报告相关API
 */
export const labReportApi = {
  // 获取检验报告主表列表
  getMainList: (visitSn) => `/b171/list/${visitSn}`,

  // 获取检验报告明细列表
  getDetailList: (labSn) => `/b1711/list/${labSn}`
}

/**
 * 病理报告相关API
 */
export const pathologyReportApi = {
  // 获取病理报告列表
  getList: (visitSn) => `/b162/list/${visitSn}`,

  // 保存病理报告
  update: '/b162/update',

  // 删除病理报告
  delete: (id) => `/b162/delete/${id}`
}

/**
 * 分子病理报告相关API
 */
export const molecularPathologyApi = {
  // 获取分子病理报告列表
  getList: (visitSn) => `/b163/list/${visitSn}`,

  // 保存分子病理报告
  update: '/b163/update',

  // 删除分子病理报告
  delete: (id) => `/b163/delete/${id}`
}

/**
 * 第三方集成相关API
 */
export const thirdPartyIntegrationApi = {
  // 获取患者历史数据（用于第三方系统集成）
  getPatientHistory: (visitSn) => `/b021/history?visitSn=${visitSn}`
}


// 导出所有API端点
export default {
  department: departmentApi,
  patient: patientApi,
  admissionRecord: admissionRecordApi,
  firstCourseRecord: firstCourseRecordApi,
  consultationRecord: consultationRecordApi,
  preoperativeDiscussion: preoperativeDiscussionApi,
  preoperativeSummary: preoperativeSummaryApi,
  invasiveProcedure: invasiveProcedureApi,
  surgeryRecord: surgeryRecordApi,
  postoperativeFirstCourseRecord: postoperativeFirstCourseRecordApi,
  dischargeRecord: dischargeRecordApi,
  dailyProgressRecord: dailyProgressRecordApi,
  examReport: examReportApi,
  tnmStagingRecord: tnmStagingRecordApi,
  diagnosisInfo: diagnosisInfoApi,
  labReport: labReportApi,
  pathologyReport: pathologyReportApi,
  molecularPathology: molecularPathologyApi,
  dictionary: dictionaryApi,
  thirdPartyIntegration: thirdPartyIntegrationApi
}
