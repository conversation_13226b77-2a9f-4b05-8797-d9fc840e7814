/**
 * 第三方系统集成服务
 * 用于与迎春花质控系统等外部系统进行集成
 */

class ThirdPartyIntegrationService {
    constructor() {
        this.clientAppSDK = null
        this.isInitialized = false
        this.config = {
            appKey: '',
            appSecretKey: '',
            deptId: '',
            doctorId: '',
            qualityTarget: '2', // 1-临床, 2-病理
            linkType: '2' // 1-app, 2-web
        }
    }

    /**
     * 初始化第三方系统SDK
     * @param {Object} config - 配置参数
     * @param {string} config.sdkUrl - SDK的URL地址
     * @param {string} config.appKey - 应用密钥
     * @param {string} config.appSecretKey - 应用秘密密钥
     * @param {string} config.deptId - 科室ID
     * @param {string} config.doctorId - 医生ID
     * @param {string} config.qualityTarget - 质控目标 (1-临床, 2-病理)
     */
    async initialize(config) {
        try {
            // 更新配置
            this.config = {...this.config, ...config}

            // 动态加载第三方SDK
            await this.loadSDK(config.sdkUrl)

            // 等待SDK加载完成
            await this.waitForSDK()

            // 配置SDK参数
            await this.configureSDK()

            this.isInitialized = true
            console.log('✅ 第三方系统SDK初始化成功')

            return true
        } catch (error) {
            console.error('❌ 第三方系统SDK初始化失败:', error)
            throw error
        }
    }

    /**
     * 动态加载第三方SDK脚本
     * @param {string} sdkUrl - SDK的URL地址
     */
    loadSDK(sdkUrl) {
        return new Promise((resolve, reject) => {
            // 检查是否已经加载
            if (document.querySelector(`script[src*="${sdkUrl}"]`)) {
                resolve()
                return
            }

            const script = document.createElement('script')
            script.src = `${sdkUrl}?linkType=${this.config.linkType}`
            script.onload = resolve
            script.onerror = reject
            document.head.appendChild(script)
        })
    }

    /**
     * 等待SDK加载完成
     */
    waitForSDK() {
        return new Promise((resolve, reject) => {
            let attempts = 0
            const maxAttempts = 50 // 最多等待5秒

            const checkSDK = () => {
                if (window._clientAppSDK) {
                    this.clientAppSDK = window._clientAppSDK
                    resolve()
                } else if (attempts < maxAttempts) {
                    attempts++
                    setTimeout(checkSDK, 100)
                } else {
                    reject(new Error('SDK加载超时'))
                }
            }

            checkSDK()
        })
    }

    /**
     * 配置SDK参数
     */
    async configureSDK() {
        if (!this.clientAppSDK) {
            throw new Error('SDK未初始化')
        }

        const params = {
            deptId: this.config.deptId,
            doctorId: this.config.doctorId,
            appKey: this.config.appKey,
            appSecretKey: this.config.appSecretKey,
            qualityTarget: this.config.qualityTarget
        }

        try {
            await this.clientAppSDK.changeParams(params)
            console.log('✅ SDK参数配置成功', params)
        } catch (error) {
            console.error('❌ SDK参数配置失败:', error)
            throw error
        }
    }

    /**
     * 发送患者数据到第三方系统
     * @param {Object} patientData - 患者数据，可以是原始格式或已格式化的历史数据
     */
    async sendPatientData(patientData) {
        if (!this.isInitialized) {
            throw new Error('SDK未初始化，请先调用initialize方法')
        }

        try {
            // 发送数据到第三方SDK
            await this.clientAppSDK.history(patientData)

            console.log('✅ 患者数据发送成功')
            return true
        } catch (error) {
            console.error('❌ 患者数据发送失败:', error)
            throw error
        }
    }

    /**
     * 格式化患者数据
     * @param {Object} patientData - 原始患者数据
     * @returns {Object} 格式化后的数据
     */
    formatPatientData(patientData) {
        const {patient, visit} = patientData

        // 根据就诊类型确定表代码
        const tableCode = visit.visitType === '门诊' ? 'b12_1' : 'b02_1'

        return {
            patientId: patient.patientId,
            visitSn: visit.visitSn,
            dataPacket: [{
                tableCode: tableCode,
                data: [{
                    patient_id: patient.patientId,
                    visit_sn: visit.visitSn,
                    visit_type: visit.visitType,
                    hospital_code: patient.hospitalCode || '',
                    hospital_name: patient.hospitalName || '',
                    visit_card_no: visit.visitCardNo || '',
                    outpatient_no: visit.outpatientNo || '',
                    visit_times: visit.visitTimes || '',
                    visit_datetime: visit.visitDatetime || '',
                    medical_record_no: visit.medicalRecordNo || '',
                    inpatient_no: visit.inpatientNo || '',
                    hospitalization_times: visit.hospitalizationTimes || '',
                    admission_datetime: visit.admissionDatetime || '',
                    discharge_datetime: visit.dischargeDatetime || '',
                    visit_doctor_no: visit.visitDoctorNo || '',
                    visit_doctor_name: visit.visitDoctorName || '',
                    name: patient.name,
                    gender: patient.gender,
                    patient_gender: patient.patientGender || 'NULL',
                    date_of_birth: patient.dateOfBirth,
                    occupation_code: patient.occupationCode || '',
                    occupation_name: patient.occupationName || '',
                    nationality: patient.nationality || '中国',
                    ethnicity: patient.ethnicity || '汉族',
                    education: patient.education || '',
                    education_code: patient.educationCode || '',
                    marital_status: patient.maritalStatus || '',
                    marital_status_code: patient.maritalStatusCode || '',
                    newbron_mark: patient.newbornMark || '否',
                    visit_status: visit.visitStatus || '否',
                    patient_identity: patient.patientIdentity || '其他',
                    blood_type_s: patient.bloodTypeS || 'NULL',
                    bolld_type_e: patient.bloodTypeE || 'NULL',
                    height: patient.height || 'NULL',
                    weight: patient.weight || 'NULL',
                    certificate_type: patient.certificateType || '身份证',
                    certificate_no: patient.certificateNo || '',
                    idcard_no: patient.idcardNo || '',
                    health_card_type: patient.healthCardType || 'NULL',
                    health_card_no: patient.healthCardNo || 'NULL',
                    insurance_type: patient.insuranceType || '',
                    insurance_no: patient.insuranceNo || '',
                    domicile_province: patient.domicileProvince || '',
                    domicile_city: patient.domicileCity || '',
                    domicile_county: patient.domicileCounty || '',
                    domicile_address: patient.domicileAddress || '',
                    home_address: patient.homeAddress || '',
                    phone_no: patient.phoneNo || '',
                    phone_no2: patient.phoneNo2 || '',
                    email: patient.email || '',
                    weixin: patient.weixin || '',
                    contact_person1: patient.contactPerson1 || '',
                    contact_phone_no1: patient.contactPhoneNo1 || '',
                    contact_person2: patient.contactPerson2 || '',
                    contact_phone_no2: patient.contactPhoneNo2 || '',
                    abo_blood_type: patient.aboBloodType || '',
                    rh_blood_type: patient.rhBloodType || '',
                    tsblbs: patient.tsblbs || 'NULL',
                    is_hospital_infected: patient.isHospitalInfected || 'NULL',
                    extend_data1: patient.extendData1 || 'NULL',
                    extend_data2: patient.extendData2 || 'NULL',
                    record_status: patient.recordStatus || '1',
                    record_datetime: patient.recordDatetime || '',
                    record_update_datetime: patient.recordUpdateDatetime || '',
                    // 住院患者特有字段
                    ...(visit.visitType === '住院' && {
                        admission_dept_code: visit.admissionDeptCode || '',
                        admission_dept_name: visit.admissionDeptName || '',
                        admission_ward_code: visit.admissionWardCode || '',
                        admission_ward_name: visit.admissionWardName || '',
                        admission_bed_code: visit.admissionBedCode || '',
                        admission_bed_name: visit.admissionBedName || '',
                        current_dept_code: visit.currentDeptCode || '',
                        current_dept_name: visit.currentDeptName || '',
                        current_ward_code: visit.currentWardCode || '',
                        current_ward_name: visit.currentWardName || '',
                        current_bed_code: visit.currentBedCode || '',
                        current_bed_name: visit.currentBedName || '',
                        admission_medical_team_code: visit.admissionMedicalTeamCode || '',
                        admission_medical_team_name: visit.admissionMedicalTeamName || '',
                        chief_physician_id: visit.chiefPhysicianId || '',
                        chief_physician: visit.chiefPhysician || '',
                        attending_physician_id: visit.attendingPhysicianId || '',
                        attending_physician: visit.attendingPhysician || '',
                        responsible_nurse_id: visit.responsibleNurseId || '',
                        responsible_nurse: visit.responsibleNurse || '',
                        admission_type_code: visit.admissionTypeCode || '',
                        admission_type_name: visit.admissionTypeName || ''
                    }),
                    // 门诊患者特有字段
                    ...(visit.visitType === '门诊' && {
                        regis_sn: visit.regisSn || '',
                        regis_datetime: visit.regisDatetime || '',
                        first_visit_mark: visit.firstVisitMark || '是',
                        regis_method_code: visit.regisMethodCode || '',
                        regis_method: visit.regisMethod || '',
                        regis_type_code: visit.regisTypeCode || '',
                        regis_type: visit.regisType || '',
                        regis_charge_price: visit.regisChargePrice || '0.000',
                        regis_paid_price: visit.regisPaidPrice || '0.000',
                        regis_dept_code: visit.regisDeptCode || '',
                        regis_dept_name: visit.regisDeptName || '',
                        technical_title: visit.technicalTitle || '',
                        job_title: visit.jobTitle || ''
                    })
                }]
            }]
        }
    }

    /**
     * 检查SDK状态
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            hasSDK: !!this.clientAppSDK,
            config: this.config
        }
    }

    /**
     * 销毁SDK连接
     */
    destroy() {
        this.clientAppSDK = null
        this.isInitialized = false
        console.log('🔄 第三方系统SDK已销毁')
    }
}

// 创建单例实例
const thirdPartyIntegration = new ThirdPartyIntegrationService()

export default thirdPartyIntegration
