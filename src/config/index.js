/**
 * 应用配置管理
 * 统一管理环境变量和应用配置
 */

// 获取环境变量
const env = import.meta.env

/**
 * 应用基础配置
 */
export const appConfig = {
  // 应用信息
  title: env.VITE_APP_TITLE || '测试医院管理系统',
  version: env.VITE_APP_VERSION || '1.0.0',
  env: env.VITE_APP_ENV || 'development',
  
  // 开发模式判断
  isDev: env.DEV,
  isProd: env.PROD,
  isTest: env.VITE_APP_ENV === 'test'
}

/**
 * API配置
 */
export const apiConfig = {
  // 基础URL - 开发环境使用代理，生产环境使用完整URL
  baseURL: env.VITE_API_BASE_URL || (env.DEV ? '/api' : 'http://localhost:6596'),

  // 后端服务器地址（仅用于参考）
  backendURL: env.VITE_BACKEND_URL || 'http://localhost:6596',

  // 请求超时时间
  timeout: parseInt(env.VITE_API_TIMEOUT) || 10000,

  // API版本
  version: 'v1',

  // 请求头配置
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },

  // 是否使用代理
  useProxy: env.DEV && env.VITE_API_BASE_URL === '/api'
}

/**
 * 获取完整的API地址
 * @param {string} endpoint - API端点
 * @returns {string} 完整的API地址
 */
export const getApiUrl = (endpoint) => {
  // 移除端点开头的斜杠（如果有的话）
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint
  return `${apiConfig.baseURL}/${cleanEndpoint}`
}

/**
 * 日志配置
 */
export const logConfig = {
  // 是否启用日志
  enabled: appConfig.isDev,
  
  // 日志级别
  level: appConfig.isDev ? 'debug' : 'error'
}

// 导出默认配置
export default {
  app: appConfig,
  api: apiConfig,
  log: logConfig,
  getApiUrl
}
