<template>
  <div class="default-layout">
    <!-- 左侧边栏 -->
    <Sidebar />
    
    <!-- 右侧主内容区 -->
    <div class="main-content">
      <!-- 顶部导航区域 -->
      <TopNavigation />
      
      <!-- 次级导航区域 -->
      <SecondaryNavigation />
      
      <!-- 主要内容区域 -->
      <div class="content-area">
        <div class="content-wrapper">
          <slot />
        </div>

        <!-- 底部版权信息 -->
        <Footer />
      </div>
    </div>
  </div>
</template>

<script setup>
import Sidebar from '@/components/layout/Sidebar.vue'
import TopNavigation from '@/components/layout/TopNavigation.vue'
import SecondaryNavigation from '@/components/layout/SecondaryNavigation.vue'
import Footer from '@/components/layout/Footer.vue'
</script>

<style scoped>
.default-layout {
  width: 100vw;
  height: 100vh;
  background-color: var(--color-bg-secondary);
  display: flex;
  overflow: hidden; /* 防止整体布局滚动 */
}

.main-content {
  width: calc(100vw - var(--sidebar-width));
  height: 100vh;
  display: flex;
  flex-direction: column;
  margin-left: var(--sidebar-width); /* 为固定的侧边栏留出空间 */
}

.content-area {
  flex: 1;
  width: 100%;
  background-color: var(--color-bg-secondary);
  padding: var(--spacing-xl) var(--spacing-xl) 0 var(--spacing-xl); /* 移除底部padding，让Footer紧贴内容 */
  overflow-y: auto; /* 仅主内容区域滚动 */
  height: calc(100vh - var(--topnav-height) - var(--secondary-nav-height)); /* 只减去顶部导航、次级导航高度，不减去Footer */
  margin-top: calc(var(--topnav-height) + var(--secondary-nav-height)); /* 为固定的顶部导航栏和次级导航栏留出空间 */

  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: var(--color-text-placeholder) var(--color-bg-secondary);
}

.content-area::-webkit-scrollbar {
  width: 6px;
}

.content-area::-webkit-scrollbar-track {
  background: var(--color-bg-tertiary);
  border-radius: var(--border-radius-sm);
}

.content-area::-webkit-scrollbar-thumb {
  background: var(--color-border-base);
  border-radius: var(--border-radius-sm);
}

.content-area::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-dark);
}

.content-wrapper {
  flex: 1;
  min-height: calc(100vh - var(--topnav-height) - var(--secondary-nav-height) - var(--footer-height)); /* 确保内容区域至少占满视口减去导航栏和Footer的高度 */
  padding-bottom: var(--spacing-xl); /* 内容与Footer之间的间距 */
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .default-layout {
    flex-direction: column;
    overflow: auto; /* 移动端允许整体滚动 */
  }

  .main-content {
    width: 100%;
    height: calc(100vh - var(--sidebar-height-mobile));
    margin-left: 0; /* 移动端取消左边距 */
  }

  .content-area {
    height: calc(100vh - var(--topnav-height-mobile) - var(--secondary-nav-height-mobile)); /* 移动端调整高度，不减去Footer */
    margin-top: calc(var(--topnav-height-mobile) + var(--secondary-nav-height-mobile)); /* 保持顶部导航栏间距 */
  }

  .content-wrapper {
    min-height: calc(100vh - var(--topnav-height-mobile) - var(--secondary-nav-height-mobile) - var(--footer-height-mobile)); /* 移动端Footer高度调整 */
  }
}

@media screen and (min-width: 769px) and (max-width: 1024px) {
  .main-content {
    width: calc(100vw - var(--sidebar-width-tablet));
    margin-left: var(--sidebar-width-tablet); /* 平板端调整左边距 */
  }

  .content-area {
    height: calc(100vh - var(--topnav-height) - var(--secondary-nav-height)); /* 平板端不减去Footer */
  }

  .content-wrapper {
    min-height: calc(100vh - var(--topnav-height) - var(--secondary-nav-height) - var(--footer-height)); /* 平板端Footer高度 */
  }
}
</style>
