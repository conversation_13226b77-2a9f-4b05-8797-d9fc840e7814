<template>
  <div class="patient-detail-layout">
    <!-- 左侧病历菜单区域 -->
    <div class="medical-menu-sidebar">
      <!-- 顶部Logo区域 -->
      <LogoSection />

      <!-- 病历菜单 -->
      <div class="menu-container">
        <slot name="medical-menu" />
      </div>
    </div>

    <!-- 右侧主内容区 -->
    <div class="main-content">
      <!-- 顶部导航区域 -->
      <TopNavigation />

      <!-- 次级导航区域（包含患者列表按钮） -->
      <SecondaryNavigation />

      <!-- 主要内容区域 -->
      <div class="content-area">
        <div class="content-wrapper">
          <slot />
        </div>

        <!-- 底部版权信息 -->
        <Footer />
      </div>
    </div>
  </div>
</template>

<script setup>
import TopNavigation from '@/components/layout/TopNavigation.vue'
import SecondaryNavigation from '@/components/layout/SecondaryNavigation.vue'
import Footer from '@/components/layout/Footer.vue'
import LogoSection from '@/components/business/LogoSection.vue'
</script>

<style scoped>
.patient-detail-layout {
  width: 100vw;
  height: 100vh;
  background-color: #F4F5F7;
  display: flex;
  overflow: hidden; /* 防止整体页面滚动 */
}

.medical-menu-sidebar {
  width: 220px;
  height: 100vh;
  background: linear-gradient(101deg, #BBD6FF 0%, #DAEFFF 21%, #FCFEFF 53%, #FFFFFF 100%);
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  position: fixed; /* 固定定位 */
  left: 0;
  top: 0;
  z-index: 1000; /* 确保在其他元素之上 */
}

.menu-container {
  flex: 1;
  overflow-y: auto;

  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.menu-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.main-content {
  width: calc(100vw - 220px);
  height: 100vh;
  display: flex;
  flex-direction: column;
  margin-left: 220px; /* 为固定的侧边栏留出空间 */
}

.content-area {
  flex: 1;
  width: 100%;
  background-color: #F4F5F7;
  padding: 20px 20px 0 20px; /* 移除底部padding，让Footer紧贴内容 */
  overflow-y: auto; /* 仅主内容区域滚动 */
  height: calc(100vh - 60px - var(--secondary-nav-height)); /* 只减去顶部导航、次级导航高度，不减去Footer */
  margin-top: 108px; /* 为固定的顶部导航栏和次级导航栏留出空间 (60px + var(--secondary-nav-height)) */

  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #C0C4CC #F4F5F7;
}

.content-area::-webkit-scrollbar {
  width: 8px;
}

.content-area::-webkit-scrollbar-track {
  background: #F4F5F7;
}

.content-area::-webkit-scrollbar-thumb {
  background: #C0C4CC;
  border-radius: 4px;
}

.content-area::-webkit-scrollbar-thumb:hover {
  background: #A8ABB2;
}

.content-wrapper {
  flex: 1;
  min-height: calc(100vh - 60px - var(--secondary-nav-height) - 60px); /* 确保内容区域至少占满视口减去导航栏和Footer的高度 */
  padding-bottom: 20px; /* 内容与Footer之间的间距 */
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .patient-detail-layout {
    flex-direction: column;
    overflow: auto; /* 移动端允许整体滚动 */
  }

  .medical-menu-sidebar {
    width: 100%;
    height: 60px;
    flex-direction: row;
    padding: 0 16px;
    overflow-x: auto;
    position: relative; /* 移动端取消固定定位 */
  }

  .main-content {
    width: 100%;
    height: calc(100vh - 60px);
    margin-left: 0; /* 移动端取消左边距 */
  }

  .content-area {
    height: calc(100vh - 60px - var(--secondary-nav-height)); /* 移动端调整高度，不减去Footer */
    margin-top: 108px; /* 保持顶部导航栏间距 */
  }

  .content-wrapper {
    min-height: calc(100vh - 60px - var(--secondary-nav-height) - 50px); /* 移动端Footer高度调整 */
  }
}

@media screen and (min-width: 769px) and (max-width: 1024px) {
  .medical-menu-sidebar {
    width: 180px;
  }

  .menu-container {
    padding: 6px 0;
  }

  .main-content {
    width: calc(100vw - 180px);
    margin-left: 180px; /* 平板端调整左边距 */
  }

  .content-area {
    height: calc(100vh - 60px - var(--secondary-nav-height)); /* 平板端不减去Footer */
  }

  .content-wrapper {
    min-height: calc(100vh - 60px - var(--secondary-nav-height) - 60px); /* 平板端Footer高度 */
  }
}
</style>
