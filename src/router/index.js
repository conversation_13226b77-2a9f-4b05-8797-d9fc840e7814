/**
 * Vue Router 主配置文件
 * 医疗系统路由管理
 */

import { createRouter, createWebHistory } from 'vue-router'
import { medicalRoutes } from './routes/medical.js'

/**
 * 路由配置
 * 支持懒加载和代码分割，提高应用性能
 */
const routes = [
  // 首页路由 - 关键路由，优先加载
  {
    path: '/',
    name: 'Home',
    component: () => import(
      /* webpackChunkName: "home" */
      /* webpackPreload: true */
      '@/views/Home.vue'
    ),
    meta: {
      title: '测试医院 - 首页',
      requiresAuth: false,
      keepAlive: true,
      preload: true // 标记为预加载
    }
  },

  // 医疗模块路由
  ...medicalRoutes,



  // 患者卡片集成测试页面
  {
    path: '/patient-integration-demo',
    name: 'PatientIntegrationDemo',
    component: () => import(
      /* webpackChunkName: "integration-demo" */
      '@/examples/PatientCardIntegration.vue'
    ),
    meta: {
      title: '患者卡片集成测试',
      requiresAuth: false,
      keepAlive: true
    }
  },



  // 404 页面 - 懒加载
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import(
      /* webpackChunkName: "error-pages" */
      '@/views/Home.vue' // 暂时重定向到首页
    ),
    meta: {
      title: '页面未找到'
    }
  }

  // 用户模块路由（预留，待创建对应组件后启用）
  // {
  //   path: '/user',
  //   name: 'User',
  //   redirect: '/user/profile',
  //   meta: {
  //     title: '用户中心',
  //     requiresAuth: true
  //   },
  //   children: [
  //     {
  //       path: 'profile',
  //       name: 'UserProfile',
  //       component: () => import('@/views/user/Profile.vue'),
  //       meta: {
  //         title: '个人资料',
  //         requiresAuth: true
  //       }
  //     },
  //     {
  //       path: 'settings',
  //       name: 'UserSettings',
  //       component: () => import('@/views/user/Settings.vue'),
  //       meta: {
  //         title: '用户设置',
  //         requiresAuth: true
  //       }
  //     }
  //   ]
  // }
]

/**
 * 创建路由实例
 */
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 路由切换时的滚动行为
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 性能监控变量
let routeStartTime = 0
const routePerformanceData = new Map()

/**
 * 全局前置守卫
 * 处理路由权限、页面标题、性能监控等
 */
router.beforeEach((to, from, next) => {
  // 开始性能监控
  routeStartTime = performance.now()

  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title
  }

  // 权限检查（预留）
  if (to.meta.requiresAuth) {
    // TODO: 检查用户登录状态
    // const userStore = useUserStore()
    // if (!userStore.isLoggedIn) {
    //   next('/login')
    //   return
    // }
  }

  // 路由加载进度
  console.log(`🚀 路由导航开始: ${from.path} -> ${to.path}`)

  // 显示加载状态（可选）
  if (to.meta.showLoading !== false) {
    // TODO: 显示全局加载指示器
    // showGlobalLoading()
  }

  next()
})

/**
 * 全局后置钩子
 * 处理路由切换完成后的逻辑和性能统计
 */
router.afterEach((to, from) => {
  // 计算路由切换时间
  const routeEndTime = performance.now()
  const navigationTime = routeEndTime - routeStartTime

  // 存储性能数据
  const performanceInfo = {
    from: from.path,
    to: to.path,
    navigationTime: Math.round(navigationTime),
    timestamp: new Date().toISOString(),
    routeName: to.name
  }

  // 保存性能数据（最多保留100条记录）
  routePerformanceData.set(to.path, performanceInfo)
  if (routePerformanceData.size > 100) {
    const firstKey = routePerformanceData.keys().next().value
    routePerformanceData.delete(firstKey)
  }

  // 控制台输出性能信息
  console.log(`✅ 路由切换完成: ${from.path} -> ${to.path} (${navigationTime.toFixed(2)}ms)`)

  // 性能警告（超过1秒的路由切换）
  if (navigationTime > 1000) {
    console.warn(`⚠️ 路由切换较慢: ${to.path} 耗时 ${navigationTime.toFixed(2)}ms`)
  }

  // 隐藏加载状态
  if (to.meta.showLoading !== false) {
    // TODO: 隐藏全局加载指示器
    // hideGlobalLoading()
  }

  // 页面访问统计（预留）
  // trackPageView(to.path, to.name, navigationTime)
})

/**
 * 路由错误处理
 */
router.onError((error) => {
  console.error('路由错误:', error)
  
  // 可以在这里添加错误上报逻辑
})

/**
 * 性能监控工具函数
 */
export const getRoutePerformanceData = () => {
  return Array.from(routePerformanceData.values()).sort((a, b) =>
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  )
}

export const getAverageNavigationTime = () => {
  const data = Array.from(routePerformanceData.values())
  if (data.length === 0) return 0

  const total = data.reduce((sum, item) => sum + item.navigationTime, 0)
  return Math.round(total / data.length)
}

export const getSlowRoutes = (threshold = 500) => {
  return Array.from(routePerformanceData.values())
    .filter(item => item.navigationTime > threshold)
    .sort((a, b) => b.navigationTime - a.navigationTime)
}

/**
 * 组件预加载函数
 */
export const preloadRoute = (routeName) => {
  const route = router.getRoutes().find(r => r.name === routeName)
  if (route && route.component && typeof route.component === 'function') {
    // 预加载组件
    route.component().catch(err => {
      console.warn(`预加载路由组件失败: ${routeName}`, err)
    })
  }
}

/**
 * 批量预加载关键路由
 */
export const preloadCriticalRoutes = () => {
  const criticalRoutes = router.getRoutes()
    .filter(route => route.meta?.preload)
    .map(route => route.name)

  criticalRoutes.forEach(routeName => {
    if (routeName) {
      preloadRoute(routeName)
    }
  })

  console.log(`🚀 预加载关键路由: ${criticalRoutes.join(', ')}`)
}

export default router

/**
 * 路由使用指南：
 * 
 * 1. 编程式导航：
 *    import { useRouter } from 'vue-router'
 *    const router = useRouter()
 *    router.push('/medical/departments')
 * 
 * 2. 声明式导航：
 *    <router-link to="/medical/departments">科室管理</router-link>
 * 
 * 3. 路由参数：
 *    router.push({ name: 'DepartmentDetail', params: { id: 123 } })
 * 
 * 4. 查询参数：
 *    router.push({ path: '/medical/patients', query: { status: 'active' } })
 * 
 * 5. 路由守卫：
 *    在组件中使用 beforeRouteEnter, beforeRouteUpdate, beforeRouteLeave
 */
