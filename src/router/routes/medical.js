/**
 * 医疗模块路由配置
 * 包含科室管理、患者管理、预约管理等医疗业务路由
 */

export const medicalRoutes = [
  // 医疗模块路由
  {
    path: '/medical',
    name: 'Medical',
    redirect: '/medical/departments',
    meta: {
      title: '医疗管理',
      requiresAuth: true,
      icon: 'medical-bag'
    },
    children: [
      // 科室管理
      {
        path: 'departments',
        name: 'DepartmentList',
        component: () => import(
          /* webpackChunkName: "medical-departments" */
          '@/views/medical/DepartmentList.vue'
        ),
        meta: {
          title: '科室管理',
          requiresAuth: true,
          keepAlive: true,
          icon: 'department'
        }
      },

      // 患者管理
      {
        path: 'patients',
        name: 'PatientManagement',
        component: () => import(
          /* webpackChunkName: "medical-patients" */
          '@/views/medical/PatientManagement.vue'
        ),
        meta: {
          title: '患者管理',
          requiresAuth: true,
          keepAlive: true,
          icon: 'patient'
        }
      },

      // 分子病理报告测试页面
      {
        path: 'molecular-pathology-test',
        name: 'MolecularPathologyTest',
        component: () => import(
          /* webpackChunkName: "molecular-pathology-test" */
          '@/views/test/MolecularPathologyTest.vue'
        ),
        meta: {
          title: '分子病理报告测试',
          requiresAuth: false,
          keepAlive: false,
          icon: 'test'
        }
      },

      // 患者详情第三方集成测试页面
      {
        path: 'patient-detail-integration-test',
        name: 'PatientDetailIntegrationTest',
        component: () => import(
          /* webpackChunkName: "patient-detail-integration-test" */
          '@/views/test/PatientDetailIntegrationTest.vue'
        ),
        meta: {
          title: '患者详情第三方集成测试',
          requiresAuth: false,
          keepAlive: false,
          icon: 'test'
        }
      },

      // 患者详情
      {
        path: 'patients/:visitSn',
        name: 'PatientDetail',
        component: () => import(
          /* webpackChunkName: "medical-patient-detail" */
          '@/views/medical/PatientDetail.vue'
        ),
        meta: {
          title: '患者详情',
          requiresAuth: true,
          keepAlive: false,
          icon: 'patient-detail'
        },
        props: true
      }
    ]
  }
]

/**
 * 完整的医疗模块路由配置（待组件创建后启用）
 *
 * 当创建了对应的Vue组件文件后，可以将以下配置复制到 medicalRoutes 数组中：
 *
 * export const medicalRoutesComplete = [
 *   {
 *     path: '/medical',
 *     name: 'Medical',
 *     redirect: '/medical/departments',
 *     meta: {
 *       title: '医疗管理',
 *       requiresAuth: true,
 *       icon: 'medical-bag'
 *     },
 *     children: [
 *       // 科室管理
 *       {
 *         path: 'departments',
 *         name: 'DepartmentList',
 *         component: () => import('@/views/medical/DepartmentList.vue'),
 *         meta: {
 *           title: '科室管理',
 *           requiresAuth: true,
 *           keepAlive: true,
 *           icon: 'department'
 *         }
 *       },
 *       {
 *         path: 'departments/:id',
 *         name: 'DepartmentDetail',
 *         component: () => import('@/views/medical/DepartmentDetail.vue'),
 *         meta: {
 *           title: '科室详情',
 *           requiresAuth: true,
 *           keepAlive: false
 *         },
 *         props: true
 *       },
 *
 *       // 患者管理
 *       {
 *         path: 'patients',
 *         name: 'PatientManagement',
 *         component: () => import('@/views/medical/PatientManagement.vue'),
 *         meta: {
 *           title: '患者管理',
 *           requiresAuth: true,
 *           keepAlive: true,
 *           icon: 'patient'
 *         }
 *       },
 *       {
 *         path: 'patients/:id',
 *         name: 'PatientDetail',
 *         component: () => import('@/views/medical/PatientDetail.vue'),
 *         meta: {
 *           title: '患者详情',
 *           requiresAuth: true,
 *           keepAlive: false
 *         },
 *         props: true
 *       },
 *
 *       // 预约管理
 *       {
 *         path: 'appointments',
 *         name: 'AppointmentSchedule',
 *         component: () => import('@/views/medical/AppointmentSchedule.vue'),
 *         meta: {
 *           title: '预约管理',
 *           requiresAuth: true,
 *           keepAlive: true,
 *           icon: 'calendar'
 *         }
 *       },
 *       {
 *         path: 'appointments/:id',
 *         name: 'AppointmentDetail',
 *         component: () => import('@/views/medical/AppointmentDetail.vue'),
 *         meta: {
 *           title: '预约详情',
 *           requiresAuth: true,
 *           keepAlive: false
 *         },
 *         props: true
 *       },
 *
 *       // 医生管理
 *       {
 *         path: 'doctors',
 *         name: 'DoctorManagement',
 *         component: () => import('@/views/medical/DoctorManagement.vue'),
 *         meta: {
 *           title: '医生管理',
 *           requiresAuth: true,
 *           keepAlive: true,
 *           icon: 'doctor'
 *         }
 *       },
 *       {
 *         path: 'doctors/:id',
 *         name: 'DoctorDetail',
 *         component: () => import('@/views/medical/DoctorDetail.vue'),
 *         meta: {
 *           title: '医生详情',
 *           requiresAuth: true,
 *           keepAlive: false
 *         },
 *         props: true
 *       },
 *
 *       // 病历管理
 *       {
 *         path: 'records',
 *         name: 'MedicalRecords',
 *         component: () => import('@/views/medical/MedicalRecords.vue'),
 *         meta: {
 *           title: '病历管理',
 *           requiresAuth: true,
 *           keepAlive: true,
 *           icon: 'record'
 *         }
 *       },
 *       {
 *         path: 'records/:id',
 *         name: 'MedicalRecordDetail',
 *         component: () => import('@/views/medical/MedicalRecordDetail.vue'),
 *         meta: {
 *           title: '病历详情',
 *           requiresAuth: true,
 *           keepAlive: false
 *         },
 *         props: true
 *       },
 *
 *       // 统计报表
 *       {
 *         path: 'statistics',
 *         name: 'MedicalStatistics',
 *         component: () => import('@/views/medical/Statistics.vue'),
 *         meta: {
 *           title: '统计报表',
 *           requiresAuth: true,
 *           keepAlive: true,
 *           icon: 'chart'
 *         }
 *       }
 *     ]
 *   }
 * ]
 *
 * 医疗模块路由说明：
 *
 * 1. 路由结构：
 *    /medical/departments - 科室管理列表
 *    /medical/departments/:id - 科室详情
 *    /medical/patients - 患者管理
 *    /medical/appointments - 预约管理
 *    /medical/doctors - 医生管理
 *    /medical/records - 病历管理
 *    /medical/statistics - 统计报表
 *
 * 2. 路由元信息：
 *    - title: 页面标题
 *    - requiresAuth: 是否需要登录
 *    - keepAlive: 是否缓存组件
 *    - icon: 菜单图标（用于导航菜单）
 *
 * 3. 懒加载：
 *    所有组件都使用动态导入，实现代码分割
 *
 * 4. 参数传递：
 *    详情页面使用 props: true 自动将路由参数作为组件props
 */
