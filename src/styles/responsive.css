/**
 * 响应式设计样式
 * 定义断点和响应式工具类
 */

/* ========== 断点定义 ========== */

:root {
  /* 断点变量 */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  --breakpoint-3xl: 1920px;
}

/* ========== 移动端优先响应式工具类 ========== */

/* 小屏幕手机 (最大宽度 767px) */
@media screen and (max-width: 767px) {
  .mobile\:hidden { display: none; }
  .mobile\:block { display: block; }
  .mobile\:flex { display: flex; }
  .mobile\:flex-col { flex-direction: column; }
  .mobile\:flex-row { flex-direction: row; }
  
  .mobile\:w-full { width: 100%; }
  .mobile\:h-auto { height: auto; }
  
  .mobile\:p-sm { padding: var(--spacing-sm); }
  .mobile\:p-md { padding: var(--spacing-md); }
  .mobile\:px-sm { padding-left: var(--spacing-sm); padding-right: var(--spacing-sm); }
  .mobile\:px-md { padding-left: var(--spacing-md); padding-right: var(--spacing-md); }
  
  .mobile\:text-xs { font-size: var(--font-size-xs); }
  .mobile\:text-sm { font-size: var(--font-size-sm); }
  .mobile\:text-base { font-size: var(--font-size-base); }
  
  .mobile\:text-center { text-align: center; }
  .mobile\:text-left { text-align: left; }
}

/* 平板设备 (768px - 1023px) */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .tablet\:hidden { display: none; }
  .tablet\:block { display: block; }
  .tablet\:flex { display: flex; }
  
  .tablet\:w-auto { width: auto; }
  .tablet\:w-full { width: 100%; }
  
  .tablet\:p-md { padding: var(--spacing-md); }
  .tablet\:p-lg { padding: var(--spacing-lg); }
  
  .tablet\:text-sm { font-size: var(--font-size-sm); }
  .tablet\:text-base { font-size: var(--font-size-base); }
  .tablet\:text-md { font-size: var(--font-size-md); }
}

/* 桌面设备 (1024px 及以上) */
@media screen and (min-width: 1024px) {
  .desktop\:hidden { display: none; }
  .desktop\:block { display: block; }
  .desktop\:flex { display: flex; }
  
  .desktop\:w-auto { width: auto; }
  .desktop\:w-full { width: 100%; }
  
  .desktop\:p-lg { padding: var(--spacing-lg); }
  .desktop\:p-xl { padding: var(--spacing-xl); }
  
  .desktop\:text-base { font-size: var(--font-size-base); }
  .desktop\:text-md { font-size: var(--font-size-md); }
  .desktop\:text-lg { font-size: var(--font-size-lg); }
}

/* 大屏幕设备 (1920px 及以上) */
@media screen and (min-width: 1920px) {
  .large\:text-lg { font-size: var(--font-size-lg); }
  .large\:text-xl { font-size: var(--font-size-xl); }
  .large\:p-xl { padding: var(--spacing-xl); }
  .large\:p-2xl { padding: var(--spacing-2xl); }
}

/* ========== 医疗系统专用响应式样式 ========== */

/* 侧边栏响应式 */
.sidebar-responsive {
  width: var(--sidebar-width);
  height: 100vh;
  background: var(--gradient-sidebar);
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  padding: 0 var(--spacing-sm);
}

@media screen and (max-width: 768px) {
  .sidebar-responsive {
    width: 100%;
    height: var(--sidebar-height-mobile);
    background: var(--gradient-sidebar-mobile);
    flex-direction: row;
    padding: 0 var(--spacing-lg);
    overflow-x: auto;
  }
}

@media screen and (min-width: 769px) and (max-width: 1024px) {
  .sidebar-responsive {
    width: var(--sidebar-width-tablet);
  }
}

/* 主内容区响应式 */
.main-content-responsive {
  width: calc(100vw - var(--sidebar-width));
  height: 100vh;
  display: flex;
  flex-direction: column;
}

@media screen and (max-width: 768px) {
  .main-content-responsive {
    width: 100%;
    height: calc(100vh - var(--sidebar-height-mobile));
  }
}

@media screen and (min-width: 769px) and (max-width: 1024px) {
  .main-content-responsive {
    width: calc(100vw - var(--sidebar-width-tablet));
  }
}

/* 导航栏响应式 */
.navigation-responsive {
  width: 100%;
  height: var(--topnav-height);
  background-color: var(--color-bg-primary);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-xl);
  box-shadow: var(--shadow-sm);
}

@media screen and (max-width: 768px) {
  .navigation-responsive {
    padding: 0 var(--spacing-lg);
    height: var(--topnav-height-mobile);
  }
}

/* 次级导航响应式 */
.secondary-nav-responsive {
  width: 100%;
  height: var(--secondary-nav-height);
  background-color: var(--color-bg-primary);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-xl);
  border-bottom: 1px solid var(--color-border-light);
}

@media screen and (max-width: 768px) {
  .secondary-nav-responsive {
    padding: 0 var(--spacing-lg);
    height: var(--secondary-nav-height-mobile);
  }
}

/* 底部响应式 */
.footer-responsive {
  width: 100%;
  height: var(--footer-height);
  background-color: var(--color-bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

@media screen and (max-width: 768px) {
  .footer-responsive {
    height: var(--footer-height-mobile);
  }
}

/* ========== 容器响应式 ========== */

.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-lg);
  padding-right: var(--spacing-lg);
}

@media screen and (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media screen and (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media screen and (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media screen and (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media screen and (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

/* ========== 网格系统 ========== */

.grid {
  display: grid;
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }
.gap-xl { gap: var(--spacing-xl); }

@media screen and (max-width: 768px) {
  .mobile\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .mobile\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
  .tablet\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .tablet\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .tablet\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@media screen and (min-width: 1024px) {
  .desktop\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .desktop\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .desktop\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
}
