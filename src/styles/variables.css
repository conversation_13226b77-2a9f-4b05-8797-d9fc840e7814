/**
 * 设计系统变量定义
 * 医疗系统专业UI风格的设计token
 */

:root {
  /* ========== 颜色系统 ========== */
  
  /* 主色调 */
  --color-primary: #1678FF;
  --color-primary-light: #4A90FF;
  --color-primary-dark: #1565C0;
  --color-primary-hover: rgba(22, 120, 255, 0.1);
  --color-primary-active: rgba(22, 120, 255, 0.2);
  
  /* 功能色彩 */
  --color-success: #52C41A;
  --color-warning: #FAAD14;
  --color-error: #FF4757;
  --color-info: #1890FF;
  
  /* 文字颜色 */
  --color-text-primary: #192C4E;
  --color-text-secondary: #6B778C;
  --color-text-placeholder: #C0C4CC;
  --color-text-disabled: #E5E7EB;
  
  /* 背景颜色 */
  --color-bg-primary: #FFFFFF;
  --color-bg-secondary: #F4F5F7;
  --color-bg-tertiary: #FAFBFC;
  
  /* 边框颜色 */
  --color-border-light: #E5E7EB;
  --color-border-base: #D1D5DB;
  --color-border-dark: #9CA3AF;
  
  /* 阴影颜色 */
  --color-shadow-light: rgba(0, 0, 0, 0.1);
  --color-shadow-base: rgba(0, 0, 0, 0.15);
  --color-shadow-dark: rgba(0, 0, 0, 0.25);
  --color-shadow-blue: rgba(22, 120, 255, 0.25);

  /* 渐变背景 */
  --gradient-sidebar: linear-gradient(101deg, #BBD6FF 0%, #DAEFFF 21%, #FCFEFF 53%, #FFFFFF 100%);
  --gradient-sidebar-mobile: linear-gradient(90deg, #BBD6FF 0%, #DAEFFF 21%, #FCFEFF 53%, #FFFFFF 100%);
  
  /* ========== 字体系统 ========== */
  
  /* 字体族 */
  --font-family-primary: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-family-secondary: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  
  /* 字体大小 */
  --font-size-xs: 10px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 22px;
  --font-size-3xl: 24px;
  
  /* 字体粗细 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-base: 1.4;
  --line-height-relaxed: 1.6;
  
  /* ========== 间距系统 ========== */
  
  /* 基础间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 32px;
  --spacing-4xl: 40px;
  --spacing-5xl: 48px;
  --spacing-6xl: 64px;
  
  /* ========== 尺寸系统 ========== */
  
  /* 组件高度 */
  --height-xs: 24px;
  --height-sm: 32px;
  --height-md: 40px;
  --height-md2: 44px;
  --height-lg: 48px;
  --height-xl: 60px;
  
  /* 布局尺寸 */
  --sidebar-width: 220px;
  --sidebar-width-tablet: 180px;
  --sidebar-height-mobile: 60px;
  --topnav-height: 60px;
  --topnav-height-mobile: 50px;
  --secondary-nav-height: 44px;
  --secondary-nav-height-mobile: 40px;
  --footer-height: 60px;
  --footer-height-mobile: 50px;
  
  /* ========== 圆角系统 ========== */
  
  --border-radius-xs: 2px;
  --border-radius-sm: 4px;
  --border-radius-md: 6px;
  --border-radius-lg: 8px;
  --border-radius-xl: 12px;
  --border-radius-full: 50%;
  
  /* ========== 阴影系统 ========== */
  
  --shadow-xs: 0 1px 2px var(--color-shadow-light);
  --shadow-sm: 0 1px 3px var(--color-shadow-light);
  --shadow-md: 0 2px 4px var(--color-shadow-base);
  --shadow-lg: 0 4px 8px var(--color-shadow-base);
  --shadow-xl: 0 8px 16px var(--color-shadow-dark);
  --shadow-bl: 0 8px 16px var(--color-shadow-blue);

  /* ========== 过渡动画 ========== */
  
  --transition-fast: 0.15s ease;
  --transition-base: 0.2s ease;
  --transition-slow: 0.3s ease;
  
  /* ========== 层级系统 ========== */
  
  --z-index-dropdown: 1000;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;
}
