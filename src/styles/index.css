/**
 * 样式系统统一导出
 * 医疗系统设计系统入口文件
 */

/* 导入设计变量 */
@import './variables.css';

/* 导入公共样式类 */
@import './common.css';

/* 导入响应式设计样式 */
@import './responsive.css';

/* ========== 全局基础样式重置 ========== */

/* 基础重置 */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* HTML和Body基础样式 */
html {
  font-size: var(--font-size-md);
  line-height: var(--line-height-relaxed);
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-secondary);
  line-height: var(--line-height-base);
  width: 100%;
  min-height: 100%;
}

/* 应用根容器 */
#app {
  width: 100%;
  height: 100%;
  min-height: 100vh;
}

/* ========== 元素基础样式 ========== */

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
  margin: 0;
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-md); }
h6 { font-size: var(--font-size-base); }

/* 段落样式 */
p {
  margin: 0;
  line-height: var(--line-height-base);
}

/* 链接样式 */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: var(--transition-base);
}

a:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--color-primary-hover);
  outline-offset: 2px;
}

/* 按钮基础样式 */
button {
  font-family: var(--font-family-primary);
  cursor: pointer;
  border: none;
  background: none;
  padding: 0;
  margin: 0;
  transition: var(--transition-base);
}

button:focus {
  outline: 2px solid var(--color-primary-hover);
  outline-offset: 2px;
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 输入框基础样式 */
input, textarea, select {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  border: 1px solid var(--color-border-light);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-base);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: none !important; /* 移除内阴影，避免高度变化 */
}

/* 图片样式 */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* 列表样式 */
ul, ol {
  list-style: none;
  margin: 0;
  padding: 0;
}

/* 表格样式 */
table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
}

/* ========== 滚动条样式 ========== */

/* Webkit浏览器滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-tertiary);
  border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-base);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-base);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-dark);
}

/* Firefox滚动条 */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border-base) var(--color-bg-tertiary);
}

/* ========== 选择文本样式 ========== */

::selection {
  background-color: var(--color-primary-hover);
  color: var(--color-primary);
}

::-moz-selection {
  background-color: var(--color-primary-hover);
  color: var(--color-primary);
}

/* ========== 医疗系统专用全局样式 ========== */

/* 应用医疗主题到根元素 */
:root {
  /* Element Plus 主题变量覆盖 */
  --el-color-primary: var(--color-primary);
  --el-color-primary-light-3: var(--color-primary-hover);
  --el-color-primary-light-5: var(--color-primary-active);
  --el-color-primary-dark-2: var(--color-primary-dark);

  --el-color-success: var(--color-success);
  --el-color-warning: var(--color-warning);
  --el-color-danger: var(--color-error);
  --el-color-info: var(--color-info);

  --el-text-color-primary: var(--color-text-primary);
  --el-text-color-regular: var(--color-text-secondary);
  --el-text-color-secondary: var(--color-text-placeholder);
  --el-text-color-placeholder: var(--color-text-disabled);

  --el-bg-color: var(--color-bg-primary);
  --el-bg-color-page: var(--color-bg-secondary);

  --el-border-color: var(--color-border-light);
  --el-border-color-light: var(--color-border-light);
  --el-border-color-lighter: var(--color-border-light);

  --el-font-family: var(--font-family-primary);
}

/* ========== Element Plus 全局样式覆盖 ========== */

/* 移除所有输入框组件的内阴影效果 */

/* Input 输入框 */
.el-input__wrapper {
  box-shadow: none !important;
}

.el-input__wrapper:hover {
  box-shadow: none !important;
}

.el-input__wrapper.is-focus,
.el-input__wrapper:focus,
.el-input__wrapper:focus-within {
  box-shadow: none !important;
}

/* Textarea 文本域 */
.el-textarea__inner {
  box-shadow: none !important;
}

.el-textarea__inner:hover {
  box-shadow: none !important;
}

.el-textarea__inner:focus {
  box-shadow: none !important;
}

/* Select 选择器 */
.el-select__wrapper {
  box-shadow: none !important;
}

.el-select__wrapper:hover {
  box-shadow: none !important;
}

.el-select__wrapper.is-focused,
.el-select__wrapper:focus,
.el-select__wrapper:focus-within {
  box-shadow: none !important;
}

/* DatePicker 日期选择器 - 完整覆盖 */
.el-date-editor .el-input__wrapper {
  box-shadow: none !important;
}

.el-date-editor:hover .el-input__wrapper {
  box-shadow: none !important;
}

.el-date-editor .el-input__wrapper.is-focus,
.el-date-editor.is-focus .el-input__wrapper,
.el-date-editor .el-input__wrapper:focus,
.el-date-editor .el-input__wrapper:focus-within {
  box-shadow: none !important;
}

/* 日期选择器的所有可能状态 */
.el-date-editor--date .el-input__wrapper,
.el-date-editor--datetime .el-input__wrapper,
.el-date-editor--daterange .el-input__wrapper,
.el-date-editor--datetimerange .el-input__wrapper,
.el-date-editor--month .el-input__wrapper,
.el-date-editor--year .el-input__wrapper,
.el-date-editor--week .el-input__wrapper {
  box-shadow: none !important;
}

.el-date-editor--date:hover .el-input__wrapper,
.el-date-editor--datetime:hover .el-input__wrapper,
.el-date-editor--daterange:hover .el-input__wrapper,
.el-date-editor--datetimerange:hover .el-input__wrapper,
.el-date-editor--month:hover .el-input__wrapper,
.el-date-editor--year:hover .el-input__wrapper,
.el-date-editor--week:hover .el-input__wrapper {
  box-shadow: none !important;
}

.el-date-editor--date .el-input__wrapper.is-focus,
.el-date-editor--datetime .el-input__wrapper.is-focus,
.el-date-editor--daterange .el-input__wrapper.is-focus,
.el-date-editor--datetimerange .el-input__wrapper.is-focus,
.el-date-editor--month .el-input__wrapper.is-focus,
.el-date-editor--year .el-input__wrapper.is-focus,
.el-date-editor--week .el-input__wrapper.is-focus,
.el-date-editor--date .el-input__wrapper:focus,
.el-date-editor--datetime .el-input__wrapper:focus,
.el-date-editor--daterange .el-input__wrapper:focus,
.el-date-editor--datetimerange .el-input__wrapper:focus,
.el-date-editor--month .el-input__wrapper:focus,
.el-date-editor--year .el-input__wrapper:focus,
.el-date-editor--week .el-input__wrapper:focus {
  box-shadow: none !important;
}

/* TimePicker 时间选择器 */
.el-time-editor .el-input__wrapper {
  box-shadow: none !important;
}

.el-time-editor:hover .el-input__wrapper {
  box-shadow: none !important;
}

.el-time-editor .el-input__wrapper.is-focus,
.el-time-editor.is-focus .el-input__wrapper {
  box-shadow: none !important;
}

/* InputNumber 数字输入框 */
.el-input-number .el-input__wrapper {
  box-shadow: none !important;
}

.el-input-number:hover .el-input__wrapper {
  box-shadow: none !important;
}

.el-input-number .el-input__wrapper.is-focus {
  box-shadow: none !important;
}

/* Cascader 级联选择器 */
.el-cascader .el-input__wrapper {
  box-shadow: none !important;
}

.el-cascader:hover .el-input__wrapper {
  box-shadow: none !important;
}

.el-cascader .el-input__wrapper.is-focus {
  box-shadow: none !important;
}

/* ColorPicker 颜色选择器 */
.el-color-picker__trigger {
  box-shadow: none !important;
}

.el-color-picker__trigger:hover {
  box-shadow: none !important;
}

/* Rate 评分 */
.el-rate {
  box-shadow: none !important;
}

/* Slider 滑块 */
.el-slider__runway {
  box-shadow: none !important;
}

/* Switch 开关 */
.el-switch__core {
  box-shadow: none !important;
}

.el-switch__core:hover {
  box-shadow: none !important;
}

/* Upload 上传 */
.el-upload {
  box-shadow: none !important;
}

.el-upload:hover {
  box-shadow: none !important;
}

/* Transfer 穿梭框 */
.el-transfer-panel {
  box-shadow: none !important;
}

/* Form 表单项 */
.el-form-item__content {
  box-shadow: none !important;
}

/* 日期选择器通用选择器 - 最强覆盖 */
[class*="el-date-editor"] {
  box-shadow: none !important;
}

[class*="el-date-editor"]:hover {
  box-shadow: none !important;
}

[class*="el-date-editor"]:focus,
[class*="el-date-editor"].is-focus {
  box-shadow: none !important;
}

[class*="el-date-editor"] .el-input__wrapper {
  box-shadow: none !important;
}

[class*="el-date-editor"]:hover .el-input__wrapper {
  box-shadow: none !important;
}

[class*="el-date-editor"] .el-input__wrapper:focus,
[class*="el-date-editor"] .el-input__wrapper.is-focus,
[class*="el-date-editor"] .el-input__wrapper:focus-within {
  box-shadow: none !important;
}

/* 超高优先级覆盖 - 针对组件中的具体样式 */
.el-date-editor.form-date-picker .el-input__wrapper {
  box-shadow: none !important;
}

.el-date-editor.form-date-picker:hover .el-input__wrapper {
  box-shadow: none !important;
}

.el-date-editor.form-date-picker .el-input__wrapper.is-focus,
.el-date-editor.form-date-picker.is-focus .el-input__wrapper,
.el-date-editor.form-date-picker .el-input__wrapper:focus,
.el-date-editor.form-date-picker .el-input__wrapper:focus-within {
  box-shadow: none !important;
}

/* 终极覆盖 - 使用更具体的选择器 */
div.el-date-editor.form-date-picker .el-input__wrapper.is-focus,
div.el-date-editor.form-date-picker.is-focus .el-input__wrapper,
div.el-date-editor.form-date-picker .el-input__wrapper:focus {
  box-shadow: none !important;
}

/* 通用选择器 - 确保覆盖所有可能的Element Plus输入组件 */
[class*="el-input"],
[class*="el-textarea"],
[class*="el-select"],
[class*="el-date"],
[class*="el-time"],
[class*="el-cascader"],
[class*="el-color-picker"] {
  box-shadow: none !important;
}

[class*="el-input"]:hover,
[class*="el-textarea"]:hover,
[class*="el-select"]:hover,
[class*="el-date"]:hover,
[class*="el-time"]:hover,
[class*="el-cascader"]:hover,
[class*="el-color-picker"]:hover {
  box-shadow: none !important;
}

[class*="el-input"]:focus,
[class*="el-textarea"]:focus,
[class*="el-select"]:focus,
[class*="el-date"]:focus,
[class*="el-time"]:focus,
[class*="el-cascader"]:focus,
[class*="el-color-picker"]:focus,
[class*="el-input"].is-focus,
[class*="el-textarea"].is-focus,
[class*="el-select"].is-focus,
[class*="el-date"].is-focus,
[class*="el-time"].is-focus,
[class*="el-cascader"].is-focus,
[class*="el-color-picker"].is-focus {
  box-shadow: none !important;
}

/* 最终兜底选择器 - 覆盖所有可能的wrapper */
[class*="el-input__wrapper"],
[class*="el-textarea__inner"] {
  box-shadow: none !important;
}

[class*="el-input__wrapper"]:hover,
[class*="el-textarea__inner"]:hover {
  box-shadow: none !important;
}

[class*="el-input__wrapper"]:focus,
[class*="el-input__wrapper"].is-focus,
[class*="el-input__wrapper"]:focus-within,
[class*="el-textarea__inner"]:focus,
[class*="el-textarea__inner"].is-focus {
  box-shadow: none !important;
}

/* 终极全局覆盖 - 使用最高优先级 */
* [class*="el-input__wrapper"],
* [class*="el-input__wrapper"]:hover,
* [class*="el-input__wrapper"]:focus,
* [class*="el-input__wrapper"].is-focus,
* [class*="el-input__wrapper"]:focus-within {
  box-shadow: none !important;
}

/* 专门针对日期选择器的终极覆盖 */
* .el-date-editor .el-input__wrapper,
* .el-date-editor:hover .el-input__wrapper,
* .el-date-editor .el-input__wrapper:focus,
* .el-date-editor .el-input__wrapper.is-focus,
* .el-date-editor.is-focus .el-input__wrapper,
* .el-date-editor .el-input__wrapper:focus-within {
  box-shadow: none !important;
}

/* 最终解决方案 - 使用CSS变量覆盖 */
:root {
  --el-box-shadow: none !important;
  --el-box-shadow-light: none !important;
  --el-box-shadow-base: none !important;
  --el-box-shadow-dark: none !important;
}

/* 暴力覆盖所有可能的box-shadow */
.el-input__wrapper,
.el-input__wrapper:hover,
.el-input__wrapper:focus,
.el-input__wrapper.is-focus,
.el-input__wrapper:focus-within,
.el-textarea__inner,
.el-textarea__inner:hover,
.el-textarea__inner:focus,
.el-select__wrapper,
.el-select__wrapper:hover,
.el-select__wrapper:focus,
.el-select__wrapper.is-focused,
.el-date-editor .el-input__wrapper,
.el-date-editor:hover .el-input__wrapper,
.el-date-editor .el-input__wrapper:focus,
.el-date-editor .el-input__wrapper.is-focus,
.el-date-editor.is-focus .el-input__wrapper,
.el-date-editor.form-date-picker .el-input__wrapper,
.el-date-editor.form-date-picker:hover .el-input__wrapper,
.el-date-editor.form-date-picker .el-input__wrapper:focus,
.el-date-editor.form-date-picker .el-input__wrapper.is-focus,
.el-date-editor.form-date-picker.is-focus .el-input__wrapper {
  box-shadow: none !important;
}

/* ========== 打印样式 ========== */

@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a, a:visited {
    text-decoration: underline;
  }

  .no-print {
    display: none !important;
  }
}
