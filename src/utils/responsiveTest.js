/**
 * 响应式布局测试工具
 * 用于验证患者卡片在不同屏幕尺寸下的表现
 */

/**
 * 获取当前屏幕信息
 */
export function getCurrentScreenInfo() {
  return {
    width: window.innerWidth,
    height: window.innerHeight,
    devicePixelRatio: window.devicePixelRatio
  }
}

/**
 * 获取患者卡片的实际尺寸
 */
export function getPatientCardDimensions() {
  const cards = document.querySelectorAll('.patient-card')
  if (cards.length === 0) {
    return { error: '未找到患者卡片元素' }
  }
  
  const firstCard = cards[0]
  const rect = firstCard.getBoundingClientRect()
  const computedStyle = window.getComputedStyle(firstCard)
  
  return {
    count: cards.length,
    actualWidth: rect.width,
    actualHeight: rect.height,
    cssWidth: computedStyle.width,
    cssHeight: computedStyle.height,
    isFixedSize: rect.width === 400 && rect.height === 232
  }
}

/**
 * 获取卡片网格布局信息
 */
export function getCardGridInfo() {
  const grid = document.querySelector('.card-grid')
  if (!grid) {
    return { error: '未找到卡片网格元素' }
  }
  
  const computedStyle = window.getComputedStyle(grid)
  const gridColumns = computedStyle.gridTemplateColumns
  
  // 解析列数
  const columnCount = gridColumns.split(' ').length
  
  return {
    gridTemplateColumns: gridColumns,
    columnCount: columnCount,
    gap: computedStyle.gap,
    justifyContent: computedStyle.justifyContent,
    maxWidth: computedStyle.maxWidth
  }
}

/**
 * 验证响应式断点
 */
export function validateResponsiveBreakpoints() {
  const screenWidth = window.innerWidth
  const gridInfo = getCardGridInfo()
  const cardInfo = getPatientCardDimensions()
  
  // 预期的列数
  let expectedColumns = 1
  if (screenWidth >= 1920) expectedColumns = 4
  else if (screenWidth >= 1500) expectedColumns = 3
  else if (screenWidth >= 1080) expectedColumns = 2
  else expectedColumns = 1
  
  const isCorrectColumns = gridInfo.columnCount === expectedColumns
  const isFixedCardSize = cardInfo.isFixedSize
  
  return {
    screenWidth,
    expectedColumns,
    actualColumns: gridInfo.columnCount,
    isCorrectColumns,
    isFixedCardSize,
    cardDimensions: `${cardInfo.actualWidth}×${cardInfo.actualHeight}`,
    status: isCorrectColumns && isFixedCardSize ? '✅ 正确' : '❌ 错误'
  }
}

/**
 * 生成响应式测试报告
 */
export function generateResponsiveReport() {
  console.group('📱 患者卡片响应式测试报告')
  
  const screenInfo = getCurrentScreenInfo()
  const cardInfo = getPatientCardDimensions()
  const gridInfo = getCardGridInfo()
  const validation = validateResponsiveBreakpoints()
  
  console.log('🖥️ 屏幕信息:')
  console.log(`   尺寸: ${screenInfo.width}×${screenInfo.height}`)
  console.log(`   设备像素比: ${screenInfo.devicePixelRatio}`)
  
  console.log('\n🃏 卡片信息:')
  console.log(`   数量: ${cardInfo.count}`)
  console.log(`   实际尺寸: ${cardInfo.actualWidth}×${cardInfo.actualHeight}`)
  console.log(`   CSS尺寸: ${cardInfo.cssWidth} × ${cardInfo.cssHeight}`)
  console.log(`   固定尺寸: ${cardInfo.isFixedSize ? '✅ 是' : '❌ 否'}`)
  
  console.log('\n📐 网格布局:')
  console.log(`   列定义: ${gridInfo.gridTemplateColumns}`)
  console.log(`   列数: ${gridInfo.columnCount}`)
  console.log(`   间距: ${gridInfo.gap}`)
  console.log(`   对齐: ${gridInfo.justifyContent}`)
  
  console.log('\n🎯 断点验证:')
  console.log(`   屏幕宽度: ${validation.screenWidth}px`)
  console.log(`   期望列数: ${validation.expectedColumns}`)
  console.log(`   实际列数: ${validation.actualColumns}`)
  console.log(`   列数正确: ${validation.isCorrectColumns ? '✅ 是' : '❌ 否'}`)
  console.log(`   卡片尺寸: ${validation.cardDimensions}`)
  console.log(`   整体状态: ${validation.status}`)
  
  console.groupEnd()
  
  return validation
}

/**
 * 监听窗口大小变化
 */
export function startResponsiveMonitoring() {
  let resizeTimer
  
  const handleResize = () => {
    clearTimeout(resizeTimer)
    resizeTimer = setTimeout(() => {
      console.log('🔄 窗口大小变化，重新验证...')
      generateResponsiveReport()
    }, 300)
  }
  
  window.addEventListener('resize', handleResize)
  
  // 返回清理函数
  return () => {
    window.removeEventListener('resize', handleResize)
    clearTimeout(resizeTimer)
  }
}

/**
 * 开发环境自动测试
 */
if (import.meta.env.DEV) {
  // 页面加载完成后自动运行测试
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
      console.log('🔧 开发模式：自动运行响应式测试')
      generateResponsiveReport()
      
      // 启动监听
      const cleanup = startResponsiveMonitoring()
      
      // 在控制台提供手动测试方法
      window.testResponsive = generateResponsiveReport
      window.stopResponsiveMonitoring = cleanup
      
      console.log('💡 提示：调整浏览器窗口大小查看实时验证')
      console.log('💡 手动测试：window.testResponsive()')
    }, 3000)
  })
}
