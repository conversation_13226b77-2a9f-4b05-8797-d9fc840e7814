/**
 * URL参数处理工具
 * 用于获取和处理URL查询参数
 */

/**
 * 获取URL查询参数
 * @param {string} paramName - 参数名
 * @param {string} defaultValue - 默认值
 * @returns {string} 参数值
 */
export function getUrlParam(paramName, defaultValue = '') {
  try {
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.get(paramName) || defaultValue
  } catch (error) {
    console.warn('获取URL参数失败:', error)
    return defaultValue
  }
}

/**
 * 获取数据源参数
 * 根据URL中的source参数确定数据源
 * @returns {string} 数据源标识 ('ych' | 'xrk')
 */
export function getDataSource() {
  const source = getUrlParam('source', 'ych')
  
  // 验证source参数的有效性
  const validSources = ['ych', 'xrk']
  if (!validSources.includes(source)) {
    console.warn(`无效的数据源参数: ${source}，使用默认值 'ych'`)
    return 'ych'
  }
  
  return source
}

/**
 * 获取数据源显示名称
 * @param {string} source - 数据源标识
 * @returns {string} 数据源显示名称
 */
export function getDataSourceName(source) {
  const sourceNames = {
    'ych': '测试医院',
    'xrk': '向日葵'
  }
  
  return sourceNames[source] || '未知数据源'
}

/**
 * 设置URL参数（不刷新页面）
 * @param {string} paramName - 参数名
 * @param {string} paramValue - 参数值
 */
export function setUrlParam(paramName, paramValue) {
  try {
    const url = new URL(window.location)
    url.searchParams.set(paramName, paramValue)
    window.history.replaceState({}, '', url)
  } catch (error) {
    console.warn('设置URL参数失败:', error)
  }
}

/**
 * 移除URL参数（不刷新页面）
 * @param {string} paramName - 参数名
 */
export function removeUrlParam(paramName) {
  try {
    const url = new URL(window.location)
    url.searchParams.delete(paramName)
    window.history.replaceState({}, '', url)
  } catch (error) {
    console.warn('移除URL参数失败:', error)
  }
}

/**
 * 获取所有URL参数
 * @returns {Object} 参数对象
 */
export function getAllUrlParams() {
  try {
    const urlParams = new URLSearchParams(window.location.search)
    const params = {}
    
    for (const [key, value] of urlParams) {
      params[key] = value
    }
    
    return params
  } catch (error) {
    console.warn('获取所有URL参数失败:', error)
    return {}
  }
}

/**
 * 检查是否为向日葵数据源
 * @returns {boolean} 是否为向日葵数据源
 */
export function isXrkSource() {
  return getDataSource() === 'xrk'
}

/**
 * 获取来源页面URL
 * @returns {string} 来源页面URL，如果没有则返回空字符串
 */
export function getReferrerUrl() {
  return getUrlParam('referrer', '')
}

/**
 * 设置来源页面URL
 * @param {string} referrerUrl - 来源页面的完整URL
 */
export function setReferrerUrl(referrerUrl) {
  if (referrerUrl && referrerUrl.trim()) {
    setUrlParam('referrer', encodeURIComponent(referrerUrl))
  }
}

/**
 * 移除来源页面URL参数
 */
export function removeReferrerUrl() {
  removeUrlParam('referrer')
}

/**
 * 检查是否有来源页面
 * @returns {boolean} 是否有来源页面
 */
export function hasReferrer() {
  const referrer = getReferrerUrl()
  return referrer && referrer.trim() !== ''
}

/**
 * 获取解码后的来源页面URL
 * @returns {string} 解码后的来源页面URL
 */
export function getDecodedReferrerUrl() {
  const referrer = getReferrerUrl()
  if (!referrer) return ''

  try {
    return decodeURIComponent(referrer)
  } catch (error) {
    console.warn('解码来源页面URL失败:', error)
    return referrer
  }
}

/**
 * 检查是否为测试医院数据源
 * @returns {boolean} 是否为测试医院数据源
 */
export function isYchSource() {
  return getDataSource() === 'ych'
}
