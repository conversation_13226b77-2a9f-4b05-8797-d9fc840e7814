/**
 * 头像资源验证工具
 * 验证所有头像文件是否存在且可访问
 */

/**
 * 验证单个头像文件
 * @param {string} imageName - 图片文件名
 * @returns {Promise<boolean>} 是否可访问
 */
async function verifyAvatarFile(imageName) {
  try {
    const imageUrl = new URL(`../assets/images/${imageName}`, import.meta.url).href
    
    // 尝试加载图片
    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => {
        console.log(`✅ ${imageName} - 加载成功`)
        resolve(true)
      }
      img.onerror = () => {
        console.log(`❌ ${imageName} - 加载失败`)
        resolve(false)
      }
      img.src = imageUrl
    })
  } catch (error) {
    console.log(`❌ ${imageName} - 路径错误:`, error.message)
    return false
  }
}

/**
 * 验证所有头像文件
 */
export async function verifyAllAvatars() {
  console.group('🖼️ 头像文件验证')
  
  const avatarFiles = [
    'boy.png',      // 儿童男性
    'girl.png',     // 儿童/青年女性
    'man.png',      // 青年男性
    'old-man.png',  // 老年男性
    'old-women.png' // 老年女性
  ]
  
  console.log('开始验证头像文件...')
  
  const results = await Promise.all(
    avatarFiles.map(file => verifyAvatarFile(file))
  )
  
  const successCount = results.filter(Boolean).length
  const totalCount = avatarFiles.length
  
  console.log(`\n📊 验证结果: ${successCount}/${totalCount} 个文件可用`)
  
  if (successCount === totalCount) {
    console.log('🎉 所有头像文件验证通过！')
  } else {
    console.log('⚠️ 部分头像文件不可用，请检查文件是否存在')
  }
  
  console.groupEnd()
  
  return successCount === totalCount
}

/**
 * 获取头像文件信息
 */
export function getAvatarInfo() {
  console.group('📋 头像文件信息')
  
  const avatarMapping = {
    'boy.png': '儿童男性 (≤12岁)',
    'girl.png': '儿童女性 (≤12岁) 和 青年女性 (13-59岁)',
    'man.png': '青年男性 (13-59岁)',
    'old-man.png': '老年男性 (≥60岁)',
    'old-women.png': '老年女性 (≥60岁)'
  }
  
  Object.entries(avatarMapping).forEach(([file, description]) => {
    const imageUrl = new URL(`../assets/images/${file}`, import.meta.url).href
    console.log(`📁 ${file}`)
    console.log(`   用途: ${description}`)
    console.log(`   路径: ${imageUrl}`)
    console.log('')
  })
  
  console.groupEnd()
}

/**
 * 开发环境自动验证
 */
if (import.meta.env.DEV) {
  // 延迟执行，确保DOM加载完成
  setTimeout(async () => {
    console.log('🔧 开发模式：自动验证头像资源')
    getAvatarInfo()
    await verifyAllAvatars()
  }, 2000)
}
