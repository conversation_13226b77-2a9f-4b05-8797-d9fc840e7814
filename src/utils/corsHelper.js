/**
 * CORS问题诊断和处理工具
 */

import { apiConfig } from '@/config'

/**
 * 检查CORS配置
 */
export const checkCorsConfig = () => {
  console.group('🔍 CORS配置检查')
  
  console.log('🌐 当前域名:', window.location.origin)
  console.log('📡 API基础URL:', apiConfig.baseURL)
  console.log('🔄 是否使用代理:', apiConfig.useProxy)
  
  if (apiConfig.useProxy) {
    console.log('✅ 使用Vite代理，应该不会有CORS问题')
  } else {
    const apiUrl = new URL(apiConfig.baseURL, window.location.origin)
    const isSameOrigin = apiUrl.origin === window.location.origin
    
    if (isSameOrigin) {
      console.log('✅ 同源请求，不会有CORS问题')
    } else {
      console.warn('⚠️ 跨域请求，可能遇到CORS问题')
      console.log('🎯 目标域名:', apiUrl.origin)
      console.log('💡 建议: 使用Vite代理或配置后端CORS')
    }
  }
  
  console.groupEnd()
}

/**
 * 测试API连接
 */
export const testApiConnection = async () => {
  console.group('🧪 API连接测试')
  
  try {
    // 测试简单的GET请求
    const testUrl = `${apiConfig.baseURL}/b032/stats/by-department`
    console.log('📤 测试请求:', testUrl)
    
    const response = await fetch(testUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      console.log('✅ API连接成功')
      console.log('📊 响应状态:', response.status)
      console.log('📋 响应头:', Object.fromEntries(response.headers.entries()))
      
      const data = await response.json()
      console.log('📦 响应数据:', data)
    } else {
      console.error('❌ API响应错误:', response.status, response.statusText)
    }
    
  } catch (error) {
    console.error('❌ API连接失败:', error.message)
    
    if (error.message.includes('CORS')) {
      console.error('🚫 CORS错误检测到')
      console.log('💡 解决方案:')
      console.log('   1. 确保Vite代理配置正确')
      console.log('   2. 重启开发服务器')
      console.log('   3. 检查后端服务器是否运行在正确端口')
    } else if (error.message.includes('fetch')) {
      console.error('🌐 网络错误')
      console.log('💡 检查项:')
      console.log('   1. 后端服务器是否启动')
      console.log('   2. 端口号是否正确')
      console.log('   3. 网络连接是否正常')
    }
  }
  
  console.groupEnd()
}

/**
 * 获取CORS错误的解决建议
 */
export const getCorsErrorSolution = (error) => {
  const solutions = []
  
  if (error.message.includes('CORS')) {
    solutions.push('🔧 配置Vite代理 (推荐)')
    solutions.push('🌐 配置后端CORS头')
    solutions.push('🔄 使用同源部署')
  }
  
  if (error.message.includes('Network')) {
    solutions.push('🔍 检查后端服务器状态')
    solutions.push('📡 验证API端点地址')
    solutions.push('🔌 检查网络连接')
  }
  
  return solutions
}

/**
 * 自动诊断CORS问题
 */
export const diagnoseCorsIssue = async () => {
  console.group('🔧 CORS问题自动诊断')
  
  // 1. 检查配置
  checkCorsConfig()
  
  // 2. 测试连接
  await testApiConnection()
  
  // 3. 提供建议
  console.log('💡 推荐配置:')
  console.log('   - 开发环境: 使用Vite代理 (/api -> 后端服务器)')
  console.log('   - 生产环境: 配置后端CORS或使用同源部署')
  
  console.groupEnd()
}

export default {
  checkCorsConfig,
  testApiConnection,
  getCorsErrorSolution,
  diagnoseCorsIssue
}
