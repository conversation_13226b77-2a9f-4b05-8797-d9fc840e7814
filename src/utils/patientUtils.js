/**
 * 患者相关工具函数
 * 处理患者数据的格式化、分类和头像系统
 */

/**
 * 年龄分类枚举
 */
export const AGE_CATEGORIES = {
  CHILD: 'child',      // 儿童: ≤ 12岁
  YOUTH: 'youth',      // 青年: 13-59岁
  ELDERLY: 'elderly'   // 老年: ≥ 60岁
}

/**
 * 年龄分类标签
 */
export const AGE_CATEGORY_LABELS = {
  [AGE_CATEGORIES.CHILD]: '儿童',
  [AGE_CATEGORIES.YOUTH]: '青年',
  [AGE_CATEGORIES.ELDERLY]: '老年'
}

/**
 * 根据年龄获取分类
 * @param {number} age - 患者年龄
 * @returns {string} 年龄分类
 */
export function getAgeCategory(age) {
  if (!age || typeof age !== 'number') {
    return AGE_CATEGORIES.YOUTH // 默认青年
  }
  
  if (age <= 12) {
    return AGE_CATEGORIES.CHILD
  } else if (age >= 60) {
    return AGE_CATEGORIES.ELDERLY
  } else {
    return AGE_CATEGORIES.YOUTH
  }
}

/**
 * 获取年龄分类标签
 * @param {number} age - 患者年龄
 * @returns {string} 年龄分类标签
 */
export function getAgeCategoryLabel(age) {
  const category = getAgeCategory(age)
  return AGE_CATEGORY_LABELS[category]
}

/**
 * 根据年龄和性别获取头像路径
 * @param {number} age - 患者年龄
 * @param {string} gender - 患者性别 ("男性" | "女性")
 * @returns {string} 头像图片路径
 */
export function getPatientAvatar(age, gender) {
  const category = getAgeCategory(age)
  const genderKey = normalizeGender(gender)

  // 动态导入图片资源（Vite方式）
  const getImageUrl = (imageName) => {
    return new URL(`../assets/images/${imageName}`, import.meta.url).href
  }

  // 使用实际的头像文件路径
  const avatarMap = {
    [`${AGE_CATEGORIES.CHILD}_male`]: getImageUrl('boy.png'),
    [`${AGE_CATEGORIES.CHILD}_female`]: getImageUrl('girl.png'),
    [`${AGE_CATEGORIES.YOUTH}_male`]: getImageUrl('man.png'),
    [`${AGE_CATEGORIES.YOUTH}_female`]: getImageUrl('girl.png'), // 青年女性使用girl.png
    [`${AGE_CATEGORIES.ELDERLY}_male`]: getImageUrl('old-man.png'),
    [`${AGE_CATEGORIES.ELDERLY}_female`]: getImageUrl('old-women.png')
  }

  const key = `${category}_${genderKey}`
  return avatarMap[key] || avatarMap[`${AGE_CATEGORIES.YOUTH}_male`] // 默认头像
}

/**
 * 标准化性别字段
 * @param {string} gender - 原始性别字段
 * @returns {string} 标准化后的性别 ("male" | "female")
 */
export function normalizeGender(gender) {
  if (!gender || typeof gender !== 'string') {
    return 'male' // 默认男性
  }
  
  const normalizedGender = gender.trim().toLowerCase()
  
  // 处理各种可能的性别表示
  if (normalizedGender.includes('女') || normalizedGender.includes('female')) {
    return 'female'
  } else if (normalizedGender.includes('男') || normalizedGender.includes('male')) {
    return 'male'
  }
  
  return 'male' // 默认男性
}

/**
 * 格式化性别显示
 * @param {string} gender - 原始性别字段
 * @returns {string} 格式化后的性别显示
 */
export function formatGender(gender) {
  const normalized = normalizeGender(gender)
  return normalized === 'female' ? '女' : '男'
}

/**
 * 格式化性别和年龄显示
 * @param {string} gender - 患者性别
 * @param {number} age - 患者年龄
 * @returns {string} 格式化后的性别年龄字符串
 */
export function formatGenderAge(gender, age) {
  const genderText = formatGender(gender)
  const ageText = age ? `${age}岁` : ''

  return `${genderText}${ageText ? ' | ' + ageText : ''}`
}

/**
 * 根据性别获取头像背景色
 * @param {string} gender - 患者性别
 * @returns {string} 头像背景色
 */
export function getAvatarColor(gender) {
  const normalized = normalizeGender(gender)
  
  const colors = {
    male: '#409EFF',    // 蓝色
    female: '#F56C6C'   // 粉色
  }
  
  return colors[normalized] || colors.male
}

/**
 * 获取头像文字（用于头像图片加载失败时的备用显示）
 * @param {string} name - 患者姓名
 * @returns {string} 头像文字
 */
export function getAvatarText(name) {
  if (!name || typeof name !== 'string') {
    return '?'
  }
  
  // 如果是脱敏姓名（包含*），取第一个字符
  if (name.includes('*')) {
    return name.charAt(0)
  }
  
  // 取姓名的最后一个字符作为头像文字
  return name.charAt(name.length - 1)
}

/**
 * 映射新API响应数据到组件所需格式
 * @param {Object} apiData - API返回的患者数据
 * @returns {Object} 组件所需的患者数据格式
 */
export function mapPatientData(apiData) {
  if (!apiData || typeof apiData !== 'object') {
    return null
  }

  // 获取头像路径
  const avatar = getPatientAvatar(apiData.patientAge, apiData.gender)

  return {
    // 新API字段（保持原有）
    medicalRecordNo: apiData.medicalRecordNo || '',
    hospitalizationCount: apiData.hospitalizationCount || 1,
    patientName: apiData.patientName || '未知患者',
    bedNo: apiData.bedNo || '未分配床位',
    attendingPhysician: apiData.attendingPhysician || '未分配',
    admissionDiagnosis: apiData.admissionDiagnosis || '暂无诊断信息',
    patientAge: apiData.patientAge || null,
    admissionTime: apiData.admissionTime || '',
    hospitalizationDays: apiData.hospitalizationDays || 0,
    gender: apiData.gender || '未知',

    // 新增字段支持
    visitSn: apiData.visitSn || null, // 重要：保留visitSn字段
    dischargeTime: apiData.dischargeTime || null,
    ethnicity: apiData.ethnicity || '汉族',
    // 添加计算字段
    avatar: avatar,
    ageCategory: getAgeCategory(apiData.patientAge),
    formattedGender: formatGender(apiData.gender),
    formattedGenderAge: formatGenderAge(apiData.gender, apiData.patientAge),
    avatarColor: getAvatarColor(apiData.gender),
    avatarText: getAvatarText(apiData.patientName),

    // 兼容旧字段（向后兼容）
    id: apiData.medicalRecordNo || apiData.id,
    name: apiData.patientName,
    age: apiData.patientAge,
    bed: apiData.bedNo,
    doctor: apiData.attendingPhysician,
    diagnosis: apiData.admissionDiagnosis,
    admissionDate: apiData.admissionTime,
    days: apiData.hospitalizationDays
  }
}

/**
 * 批量映射患者数据
 * @param {Array} apiDataList - API返回的患者数据列表
 * @returns {Array} 组件所需的患者数据列表
 */
export function mapPatientDataList(apiDataList) {
  if (!Array.isArray(apiDataList)) {
    return []
  }
  
  return apiDataList
    .map(mapPatientData)
    .filter(Boolean) // 过滤掉无效数据
}
