/**
 * 头像生成器工具
 * 用于生成默认头像或处理头像加载失败的情况
 */

/**
 * 生成SVG头像
 * @param {string} text - 显示的文字
 * @param {string} backgroundColor - 背景色
 * @param {string} textColor - 文字颜色
 * @param {number} size - 头像尺寸
 * @returns {string} SVG数据URL
 */
export function generateSVGAvatar(text, backgroundColor = '#409EFF', textColor = '#FFFFFF', size = 128) {
  const svg = `
    <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
      <circle cx="${size/2}" cy="${size/2}" r="${size/2}" fill="${backgroundColor}"/>
      <text x="${size/2}" y="${size/2}" text-anchor="middle" dominant-baseline="central" 
            font-family="PingFang SC, Arial, sans-serif" font-size="${size * 0.4}" 
            font-weight="500" fill="${textColor}">${text}</text>
    </svg>
  `
  
  return `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(svg)))}`
}

/**
 * 根据年龄分类生成默认头像
 * @param {string} ageCategory - 年龄分类
 * @param {string} gender - 性别
 * @param {string} text - 显示文字
 * @returns {string} 头像数据URL
 */
export function generateDefaultAvatar(ageCategory, gender, text) {
  // 根据年龄分类和性别选择颜色
  const colorMap = {
    child_male: '#87CEEB',      // 天蓝色
    child_female: '#FFB6C1',    // 浅粉色
    youth_male: '#409EFF',      // 蓝色
    youth_female: '#F56C6C',    // 粉色
    elderly_male: '#909399',    // 灰色
    elderly_female: '#E6A23C'   // 橙色
  }
  
  const key = `${ageCategory}_${gender}`
  const backgroundColor = colorMap[key] || '#409EFF'
  
  return generateSVGAvatar(text, backgroundColor)
}

/**
 * 创建头像加载错误时的备用处理
 * @param {HTMLImageElement} imgElement - 图片元素
 * @param {Object} patientData - 患者数据
 */
export function handleAvatarError(imgElement, patientData) {
  const { ageCategory, gender, avatarText } = patientData
  const fallbackAvatar = generateDefaultAvatar(ageCategory, gender, avatarText)
  imgElement.src = fallbackAvatar
}

/**
 * 预加载头像图片
 * @param {string} src - 图片路径
 * @returns {Promise<boolean>} 是否加载成功
 */
export function preloadAvatar(src) {
  return new Promise((resolve) => {
    const img = new Image()
    img.onload = () => resolve(true)
    img.onerror = () => resolve(false)
    img.src = src
  })
}

/**
 * 获取头像URL（带备用方案）
 * @param {Object} patientData - 患者数据
 * @returns {Promise<string>} 头像URL
 */
export async function getAvatarWithFallback(patientData) {
  const { avatar, ageCategory, gender, avatarText } = patientData
  
  // 尝试加载指定头像
  if (avatar) {
    const loaded = await preloadAvatar(avatar)
    if (loaded) {
      return avatar
    }
  }
  
  // 生成默认头像
  return generateDefaultAvatar(ageCategory, gender, avatarText)
}
