<template>
  <div class="loading-state">
    <div class="loading-text">加载中...</div>
  </div>
</template>

<script setup>
// 加载组件不需要额外的逻辑
</script>

<style scoped>
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  height: 100px;
}

.loading-text {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  position: relative;
}

.loading-text::after {
  content: '';
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  width: var(--spacing-md);
  height: var(--spacing-md);
  border: 2px solid var(--color-primary);
  border-top: 2px solid transparent;
  border-radius: var(--border-radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}
</style>
