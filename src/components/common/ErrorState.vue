<template>
  <div class="error-state">
    <div class="error-text">{{ error }}</div>
    <button v-if="showRetry" class="retry-button" @click="handleRetry">重试</button>
  </div>
</template>

<script setup>
// 定义组件props
defineProps({
  error: {
    type: String,
    required: true,
    default: '发生错误，请稍后重试'
  },
  showRetry: {
    type: Boolean,
    default: true
  }
})

// 定义组件事件
const emit = defineEmits(['retry'])

// 处理重试事件
const handleRetry = () => {
  emit('retry')
}
</script>

<style scoped>
/* 错误状态样式 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
}

.error-text {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  color: var(--color-error);
  margin-bottom: var(--spacing-md);
  line-height: var(--line-height-base);
  max-width: 180px;
}

.retry-button {
  padding: var(--spacing-xs) var(--spacing-md);
  background: var(--color-primary);
  color: var(--color-bg-primary);
  border: none;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: var(--transition-base);
}

.retry-button:hover {
  background: var(--color-primary-dark);
}
</style>
