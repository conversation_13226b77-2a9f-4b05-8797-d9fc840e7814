<template>
  <el-avatar
    :size="size"
    :src="avatarSrc"
    :style="{ backgroundColor: patient.avatarColor || getAvatarColor(patient.gender) }"
    @error="handleAvatarError"
  >
    {{ patient.avatarText || getAvatarText(patient.patientName) }}
  </el-avatar>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import {
  getAvatarText,
  getAvatarColor,
  getPatientAvatar
} from '@/utils/patientUtils.js'
import { generateDefaultAvatar } from '@/utils/avatarGenerator'

// 定义组件props
const props = defineProps({
  patient: {
    type: Object,
    required: true,
    default: () => ({})
  },
  size: {
    type: [Number, String],
    default: 60
  }
})

// 头像状态管理
const avatarSrc = ref(props.patient.avatar)

// 处理头像加载错误
const handleAvatarError = () => {
  console.log('头像加载失败，使用默认头像:', props.patient.patientName)
  const fallbackAvatar = generateDefaultAvatar(
    props.patient.ageCategory || 'youth',
    props.patient.formattedGender === '女' ? 'female' : 'male',
    props.patient.avatarText || getAvatarText(props.patient.patientName)
  )
  avatarSrc.value = fallbackAvatar
}

// 组件挂载时初始化头像
onMounted(() => {
  if (!props.patient.avatar) {
    // 如果没有头像，使用工具函数生成头像
    avatarSrc.value = getPatientAvatar(props.patient.patientAge, props.patient.gender)
  }
})
</script>

<style scoped>
/* 头像组件样式由父组件控制 */
</style>
