<!--
  API调用追踪组件
  用于监控和显示API调用情况，帮助调试重复请求问题
-->
<template>
  <div class="api-tracker" v-if="showTracker">
    <div class="tracker-header">
      <h3>🔍 API调用追踪器</h3>
      <div class="tracker-controls">
        <el-button size="small" @click="clearLogs">清空日志</el-button>
        <el-button size="small" @click="toggleTracker">
          {{ showTracker ? '隐藏' : '显示' }}
        </el-button>
      </div>
    </div>
    
    <div class="tracker-content">
      <div class="stats">
        <div class="stat-item">
          <span class="stat-label">总请求数:</span>
          <span class="stat-value">{{ totalRequests }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">重复请求:</span>
          <span class="stat-value duplicate" v-if="duplicateRequests > 0">
            {{ duplicateRequests }}
          </span>
          <span class="stat-value" v-else>0</span>
        </div>
      </div>
      
      <div class="logs" ref="logsContainer">
        <div 
          v-for="log in logs" 
          :key="log.id"
          :class="['log-item', log.type, { 'duplicate': log.isDuplicate }]"
        >
          <div class="log-header">
            <span class="log-time">{{ formatTime(log.timestamp) }}</span>
            <span class="log-method">{{ log.method }}</span>
            <span class="log-url">{{ log.url }}</span>
            <span class="log-id">[{{ log.requestId }}]</span>
            <span v-if="log.isDuplicate" class="duplicate-badge">重复</span>
          </div>
          <div class="log-details" v-if="log.duration">
            <span class="log-duration">耗时: {{ log.duration }}ms</span>
            <span class="log-status">状态: {{ log.status }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'

// 响应式数据
const showTracker = ref(true)
const logs = ref([])
const totalRequests = ref(0)
const duplicateRequests = ref(0)
const logsContainer = ref(null)

// 请求追踪Map，用于检测重复请求
const requestTracker = new Map()

// 原始的console方法
let originalConsoleGroup = null
let originalConsoleLog = null

// 监听console输出
const interceptConsole = () => {
  originalConsoleGroup = console.group
  originalConsoleLog = console.log

  console.group = function(...args) {
    const message = args[0]
    if (typeof message === 'string' && message.includes('API Request')) {
      handleApiRequest(message, args)
    } else if (typeof message === 'string' && message.includes('API Response')) {
      handleApiResponse(message, args)
    }
    return originalConsoleGroup.apply(console, args)
  }
}

// 处理API请求日志
const handleApiRequest = (message, args) => {
  const match = message.match(/API Request \[([^\]]+)\]: (\w+) (.+)/)
  if (match) {
    const [, requestId, method, url] = match
    const timestamp = new Date()
    
    // 检查是否为重复请求
    const requestKey = `${method}:${url}`
    const isDuplicate = requestTracker.has(requestKey)
    
    if (isDuplicate) {
      duplicateRequests.value++
    } else {
      requestTracker.set(requestKey, { requestId, timestamp })
    }
    
    const logEntry = {
      id: `req_${Date.now()}_${Math.random()}`,
      type: 'request',
      requestId,
      method,
      url,
      timestamp,
      isDuplicate
    }
    
    logs.value.push(logEntry)
    totalRequests.value++
    
    // 自动滚动到底部
    nextTick(() => {
      if (logsContainer.value) {
        logsContainer.value.scrollTop = logsContainer.value.scrollHeight
      }
    })
  }
}

// 处理API响应日志
const handleApiResponse = (message, args) => {
  const match = message.match(/API Response \[([^\]]+)\]: (\w+) (.+) \((\d+)ms\)/)
  if (match) {
    const [, requestId, method, url, duration] = match
    
    // 查找对应的请求日志并更新
    const requestLog = logs.value.find(log => 
      log.requestId === requestId && log.type === 'request'
    )
    
    if (requestLog) {
      requestLog.duration = parseInt(duration)
      requestLog.status = 'completed'
    }
  }
}

// 格式化时间
const formatTime = (timestamp) => {
  return timestamp.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    fractionalSecondDigits: 3
  })
}

// 清空日志
const clearLogs = () => {
  logs.value = []
  totalRequests.value = 0
  duplicateRequests.value = 0
  requestTracker.clear()
}

// 切换追踪器显示
const toggleTracker = () => {
  showTracker.value = !showTracker.value
}

// 恢复console方法
const restoreConsole = () => {
  if (originalConsoleGroup) {
    console.group = originalConsoleGroup
  }
  if (originalConsoleLog) {
    console.log = originalConsoleLog
  }
}

// 生命周期
onMounted(() => {
  interceptConsole()
})

onUnmounted(() => {
  restoreConsole()
})

// 暴露方法给父组件
defineExpose({
  clearLogs,
  toggleTracker,
  getLogs: () => logs.value,
  getStats: () => ({
    total: totalRequests.value,
    duplicates: duplicateRequests.value
  })
})
</script>

<style scoped>
.api-tracker {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 400px;
  max-height: 500px;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  font-family: 'PingFang SC', sans-serif;
}

.tracker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.tracker-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.tracker-controls {
  display: flex;
  gap: 8px;
}

.tracker-content {
  padding: 12px;
}

.stats {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #606266;
}

.stat-value {
  font-size: 12px;
  font-weight: 500;
  color: #303133;
}

.stat-value.duplicate {
  color: #f56c6c;
}

.logs {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.log-item {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f2f5;
  font-size: 11px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-item.duplicate {
  background: #fef0f0;
  border-left: 3px solid #f56c6c;
}

.log-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.log-time {
  color: #909399;
  font-family: monospace;
}

.log-method {
  color: #409eff;
  font-weight: 500;
  min-width: 40px;
}

.log-url {
  color: #303133;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.log-id {
  color: #909399;
  font-family: monospace;
}

.duplicate-badge {
  background: #f56c6c;
  color: white;
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 10px;
}

.log-details {
  display: flex;
  gap: 12px;
  color: #606266;
}

.log-duration,
.log-status {
  font-size: 10px;
}

/* 滚动条样式 */
.logs::-webkit-scrollbar {
  width: 4px;
}

.logs::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.logs::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.logs::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
