<template>
  <div class="system-notice">
    <!-- 系统提示信息容器 -->
    <div class="notice-content">
      <!-- 小喇叭图标 -->
      <img
        src="@/assets/images/speaker.png"
        alt="系统提示"
        class="notice-icon"
      />

      <!-- 提示文字内容 -->
      <span class="notice-text">
        本系统为模拟HIS的功能，医生实际工作以医院实际系统为准，本系统所有数据均为专业医生模拟数据
      </span>
    </div>

    <!-- 患者列表按钮（仅在患者详情页显示） -->
    <div v-if="showPatientListButton" class="patient-list-button-container">
      <button class="patient-list-button" @click="handlePatientListClick">
        患者列表
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getAllUrlParams } from '@/utils/urlParams'

// 路由相关
const route = useRoute()
const router = useRouter()

// 判断是否显示患者列表按钮（仅在患者详情页显示）
const showPatientListButton = computed(() => {
  return route.name === 'PatientDetail'
})

// 处理患者列表按钮点击
const handlePatientListClick = () => {
  // 保持当前URL参数，返回首页
  const currentParams = getAllUrlParams()
  console.log('患者列表按钮被点击，保持参数:', currentParams)
  router.push({
    name: 'Home',
    query: currentParams
  })
}
</script>

<style scoped>
/* 系统提示信息区域 */
.system-notice {
  width: calc(100vw - var(--sidebar-width)); /* 减去侧边栏宽度 */
  margin-top: 10px;
  height: var(--secondary-nav-height);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-xl);
  position: fixed; /* 固定定位 */
  top: var(--topnav-height); /* 位于顶部导航栏下方 */
  right: 0;
  z-index: calc(var(--z-index-dropdown) - 2); /* 确保在内容之上，但在顶部导航栏之下 */
}

/* 提示内容容器 */
.notice-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-md); /* 图标与文字之间的间距 */
}

/* 小喇叭图标样式 */
.notice-icon {
  width: var(--spacing-xl);
  height: var(--spacing-xl);
  flex-shrink: 0;
  object-fit: contain;
  vertical-align: middle;
}

/* 提示文字样式 */
.notice-text {
  width: 714px;
  height: var(--spacing-xl);
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-md);
  color: var(--color-text-secondary);
  line-height: var(--spacing-xl);
  text-align: left;
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .system-notice {
    padding: 0 var(--spacing-lg);
    height: var(--secondary-nav-height-mobile);
    width: 100%; /* 移动端占满宽度 */
    position: relative; /* 移动端取消固定定位 */
    top: auto;
  }

  .notice-content {
    gap: var(--spacing-sm);
  }

  .notice-icon {
    width: var(--spacing-lg);
    height: var(--spacing-lg);
  }

  .notice-text {
    width: auto;
    max-width: calc(100vw - 80px);
    font-size: var(--font-size-base);
    height: 18px;
    line-height: 18px;
  }
}

/* 平板适配 */
@media screen and (min-width: 769px) and (max-width: 1024px) {
  .notice-text {
    width: auto;
    max-width: 600px;
    font-size: 15px;
  }

  .notice-icon {
    width: 18px;
    height: 18px;
  }
}

/* 大屏幕适配 */
@media screen and (min-width: 1025px) {
  .notice-text {
    /* 保持设计规范的714px宽度 */
    width: 714px;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 480px) {
  .system-notice {
    padding: 0 12px;
    height: 36px;
  }

  .notice-content {
    gap: 6px;
  }

  .notice-icon {
    width: 14px;
    height: 14px;
  }

  .notice-text {
    font-size: 12px;
    height: 16px;
    line-height: 16px;
    max-width: calc(100vw - 60px);
  }
}

/* 患者列表按钮容器 */
.patient-list-button-container {
  display: flex;
  align-items: center;
}

/* 患者列表按钮样式 */
.patient-list-button {
  width: 135px;
  height: 36px;
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--color-primary);
  background-color: var(--color-bg-primary);
  cursor: pointer;
  transition: var(--transition-base);
  display: flex;
  align-items: center;
  justify-content: center;

  /* 文字样式 */
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-md);
  color: var(--color-primary);
  line-height: 22px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

.patient-list-button:hover {
  background-color: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.patient-list-button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* 确保图标加载失败时的优雅降级 */
.notice-icon:not([src]) {
  display: none;
}

.notice-icon[alt]:after {
  content: attr(alt);
  font-size: 12px;
  color: #999;
}
</style>
