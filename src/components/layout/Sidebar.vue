<template>
  <div class="sidebar">
    <!-- 顶部Logo区域 -->
    <LogoSection />
    
    <!-- 科室菜单列表 -->
    <DepartmentMenu />
  </div>
</template>

<script setup>
import LogoSection from '@/components/business/LogoSection.vue'
import DepartmentMenu from '@/components/business/DepartmentMenu.vue'
</script>

<style scoped>
.sidebar {
  width: var(--sidebar-width);
  height: 100vh;
  background: var(--gradient-sidebar);
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  position: fixed; /* 固定定位 */
  left: 0;
  top: 0;
  z-index: var(--z-index-dropdown); /* 确保在其他元素之上 */
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: var(--sidebar-height-mobile);
    background: var(--gradient-sidebar-mobile);
    flex-direction: row;
    padding: 0 var(--spacing-lg);
    overflow-x: auto;
    position: relative; /* 移动端取消固定定位 */
  }
}

/* 平板适配 */
@media screen and (min-width: 769px) and (max-width: 1024px) {
  .sidebar {
    width: var(--sidebar-width-tablet);
  }
}
</style>
