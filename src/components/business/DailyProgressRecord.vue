<template>
  <div class="daily-progress-record-container">
    <!-- 左侧患者信息详情面板 -->
    <PatientInfoPanel :patient="patient"/>

    <!-- 右侧日常病程记录内容区域 -->
    <div class="record-content-area">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container" v-loading="loading" element-loading-text="正在加载日常病程记录...">
        <div style="height: 200px;"></div>
      </div>

      <!-- 内容区域 -->
      <div v-else class="content-wrapper">
        <!-- 页面标题、时间信息和按钮行 -->
        <div class="page-header">
          <h2 class="title-text">日常病程</h2>
          <div class="header-actions">
            <div class="time-info-section">
              <div class="time-item">
                <span class="time-label">创建时间：</span>
                <span class="time-value">{{ currentRecord.recordDatetime }}</span>
              </div>
              <div class="time-item">
                <span class="time-label">更新时间：</span>
                <span class="time-value">{{ currentRecord.recordUpdateDatetime }}</span>
              </div>
            </div>
            <div class="form-actions">
              <el-button
                  class="delete-btn"
                  @click="handleDelete"
                  :loading="deleteLoading"
                  :disabled="!currentRecord.recordSn"
              >
                删除
              </el-button>
              <el-button
                  type="primary"
                  class="save-btn"
                  @click="handleSave"
                  :loading="saveLoading"
              >
                保存
              </el-button>
            </div>
          </div>
        </div>


        <!-- 主要内容区域 -->
        <div class="main-content">
          <!-- 左侧日期列表 -->
          <div class="date-list-container">
            <div class="date-list">
              <div
                  v-for="record in recordList"
                  :key="record.recordSn"
                  class="date-item"
                  :class="{ active: selectedRecordSn === record.recordSn }"
                  @click="handleDateSelect(record)"
              >
                {{ record.recordDatetime }}
              </div>

              <!-- 添加按钮 - 在列表内部的最后一行 -->
              <div class="add-button-item">
                <el-button
                    type="primary"
                    :icon="Plus"
                    class="add-record-btn"
                    @click="handleAddRecord"
                    :class="{ active: isAddingNew }"
                >
                  添加
                </el-button>
              </div>
            </div>
          </div>

          <!-- 右侧内容区域 -->
          <div class="content-area">


            <!-- 记录内容文本框 -->
            <div class="record-form">
              <el-input
                  v-model="formData.recordContent"
                  type="textarea"
                  placeholder="请输入日常病程记录内容"
                  :rows="calculateTextareaRows()"
                  class="record-textarea"
                  resize="none"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, reactive, computed, onMounted, watch, nextTick} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {Plus} from '@element-plus/icons-vue'
import {apiServices} from '@/api'
import PatientInfoPanel from './PatientInfoPanel.vue'

// 定义组件props
const props = defineProps({
  patient: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

// 定义组件事件
const emit = defineEmits(['count-updated'])

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const deleteLoading = ref(false)
const isAddingNew = ref(false) // 是否处于新建模式

// 记录列表和当前选中记录
const recordList = ref([])
const selectedRecordSn = ref('')
const currentRecord = reactive({
  recordSn: '',
  visitSn: '',
  recordDatetime: '',
  recordUpdateDatetime: ''
})

// 表单数据
const formData = reactive({
  recordContent: ''
})

/**
 * 计算文本框行数
 */
const calculateTextareaRows = () => {
  // 根据窗口高度动态计算文本框行数
  const windowHeight = window.innerHeight
  const baseRows = Math.floor((windowHeight - 400) / 24) // 24px为大约一行的高度
  return Math.max(baseRows, 10) // 最少10行
}

// ========== 数据加载方法 ==========

/**
 * 加载日常病程记录列表
 * @param {boolean} autoSelectFirst - 是否自动选中第一条记录，默认为true
 */
const loadRecordList = async (autoSelectFirst = true) => {
  if (!props.patient?.visitSn) {
    return
  }

  try {
    loading.value = true

    const records = await apiServices.dailyProgressRecord.getList(props.patient.visitSn)

    // 按日期倒序排列（最新的在前面）
    recordList.value = records


    // 只有在autoSelectFirst为true且没有当前选中记录时，才自动选中第一条
    if (autoSelectFirst && recordList.value.length > 0 && !selectedRecordSn.value) {
      await handleDateSelect(recordList.value[0])
    } else if (recordList.value.length === 0) {
      // 没有记录时清空当前记录和表单
      clearCurrentRecord()
    }
  } catch (error) {
    ElMessage.error('加载日常病程记录列表失败: ' + error.message)
    recordList.value = []
    clearCurrentRecord()
  } finally {
    loading.value = false
  }
}

/**
 * 加载指定记录的详情
 */
const loadRecordDetail = async (recordSn) => {
  if (!recordSn) {
    return
  }

  try {

    const data = await apiServices.dailyProgressRecord.getDetail(recordSn)

    // 更新当前记录数据
    Object.assign(currentRecord, {
      recordSn: data.recordSn || '',
      visitSn: data.visitSn || props.patient.visitSn,
      recordDatetime: data.recordDatetime || '',
      recordUpdateDatetime: data.recordUpdateDatetime || ''
    })

    // 更新表单数据
    formData.recordContent = data.recordContent || ''

  } catch (error) {
    ElMessage.error('加载日常病程记录详情失败: ' + error.message)
  }
}

/**
 * 清空当前记录
 */
const clearCurrentRecord = () => {
  Object.assign(currentRecord, {
    recordSn: '',
    visitSn: props.patient?.visitSn || '',
    recordDatetime: '',
    recordUpdateDatetime: ''
  })
  formData.recordContent = ''
  selectedRecordSn.value = ''
}

// ========== 事件处理方法 ==========

/**
 * 处理日期选择
 */
const handleDateSelect = async (record) => {
  isAddingNew.value = false // 退出新建模式
  selectedRecordSn.value = record.recordSn
  await loadRecordDetail(record.recordSn)
}

/**
 * 保存日常病程记录
 */
const handleSave = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法保存')
    return
  }


  try {
    saveLoading.value = true

    // 保存当前选中的记录ID，用于保存后恢复选中状态
    const previousSelectedRecordSn = selectedRecordSn.value
    const wasAddingNew = isAddingNew.value

    // 构建保存数据
    const saveData = {
      visitSn: props.patient.visitSn,
      recordSn: isAddingNew.value ? '' : currentRecord.recordSn, // 新建模式时recordSn为空
      recordContent: formData.recordContent.trim()
    }


    const result = await apiServices.dailyProgressRecord.save(saveData)

    // 更新记录信息
    if (result.recordSn) {
      currentRecord.recordSn = result.recordSn
      selectedRecordSn.value = result.recordSn
    }
    if (result.recordDatetime) {
      currentRecord.recordDatetime = result.recordDatetime
    }
    if (result.recordUpdateDatetime) {
      currentRecord.recordUpdateDatetime = result.recordUpdateDatetime
    }

    // 退出新建模式
    isAddingNew.value = false

    ElMessage.success(wasAddingNew ? '添加成功' : '保存成功')

    // 重新加载列表，但不自动选中第一条记录
    await loadRecordList(false)

    // 恢复选中状态
    if (result.recordSn) {
      // 如果是新建记录或者保存现有记录，选中保存的记录
      const targetRecord = recordList.value.find(record => record.recordSn === result.recordSn)
      if (targetRecord) {
        selectedRecordSn.value = result.recordSn
        await loadRecordDetail(result.recordSn)
      }
    } else if (previousSelectedRecordSn && !wasAddingNew) {
      // 如果保存失败但之前有选中记录，恢复之前的选中状态
      const targetRecord = recordList.value.find(record => record.recordSn === previousSelectedRecordSn)
      if (targetRecord) {
        selectedRecordSn.value = previousSelectedRecordSn
        await loadRecordDetail(previousSelectedRecordSn)
      }
    }

    // 触发数量更新事件
    emit('count-updated')
  } catch (error) {
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saveLoading.value = false
  }
}

/**
 * 删除日常病程记录
 */
const handleDelete = async () => {
  // 如果是新建模式，直接退出新建模式
  if (isAddingNew.value) {
    isAddingNew.value = false
    clearCurrentRecord()
    ElMessage.info('已取消新建')
    return
  }

  if (!currentRecord.recordSn) {
    ElMessage.error('没有可删除的记录')
    return
  }

  try {
    await ElMessageBox.confirm(
        '确定要删除这条日常病程记录吗？删除后无法恢复。',
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
    )

    deleteLoading.value = true


    await apiServices.dailyProgressRecord.delete(currentRecord.recordSn)

    ElMessage.success('删除成功')

    // 清空当前选中状态
    selectedRecordSn.value = ''

    // 重新加载列表，允许自动选中第一条记录
    await loadRecordList(true)

    // 触发数量更新事件
    emit('count-updated')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  } finally {
    deleteLoading.value = false
  }
}

/**
 * 进入新建模式
 */
const handleAddRecord = () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法添加记录')
    return
  }

  // 进入新建模式
  isAddingNew.value = true

  // 清空当前选中状态和表单
  selectedRecordSn.value = ''
  clearCurrentRecord()
  handleSave()
}

// 监听患者变化，重新加载数据
watch(() => props.patient?.visitSn, (newVisitSn) => {
  if (newVisitSn) {
    loadRecordList()
  }
}, {immediate: false})

// 组件挂载时加载数据
onMounted(async () => {
  if (props.patient?.visitSn) {
    await loadRecordList()
  }
})
</script>

<style scoped>
/* 整体容器 */
.daily-progress-record-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 180px);
  gap: 20px;
}

/* 右侧记录内容区域 */
.record-content-area {
  flex: 1;
  height: 100%;
  background: #FFFFFF;
  border-radius: 0 12px 12px 0;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 加载容器 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

/* 内容包装器 */
.content-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* 页面标题、时间信息和按钮行 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0; /* 防止压缩 */
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 40px;
}

.title-text {
  width: auto; /* 自动宽度 */
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: #172B4D;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin: 0;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧日期列表容器 */
.date-list-container {
  flex-shrink: 0;
  margin-right: -1px;
}

.date-list {
  width: 210px;
  background: #FAFCFF;
  border: 1px solid #DBE1ED;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  /* 高度自适应内容，不设置固定高度 */
}

.date-item {
  width: 208px;
  height: 46px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #172B4D;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.date-item:hover {
  background: #F4F5F7;
}

.date-item.active {
  background: #E6F1FF;
}

.empty-state {
  width: 100%;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #6B778C;
  line-height: 20px;
  flex: 1;
}

/* 添加按钮项 - 在日期列表内部 */
.add-button-item {
  width: 100%;
  padding: 8px 12px;
  margin-top: auto; /* 推到底部 */
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  border-top: 1px solid #EBECF0;
  background: #FAFCFF;
}

.add-record-btn {
  width: 80px;
  height: 32px;
  background: transparent;
  border: none;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #1678FF;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  cursor: pointer;
}

.add-record-btn:hover {
  color: #1678FF;
}

.add-record-btn:focus {
  color: #1678FF;
  outline: none;
}

/* 新建模式时的激活状态 */
.add-record-btn.active {
  color: #1678FF;
}

/* 右侧内容区域 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 时间信息头部 */
.time-info-section {
  display: flex;
  gap: 40px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-label {
  width: 80px;
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #6B778C;
  line-height: 20px;
  text-align: left;
}

.time-value {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #172B4D;
  line-height: 20px;
}

/* 表单区域 */
.record-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 文本框样式 */
.record-textarea {
  flex: 1;
  height: 100%;
}

.record-textarea :deep(.el-textarea__inner) {
  height: 100% !important;
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #EBECF0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  resize: none;
  line-height: 1.5;
  padding: 12px;
}

/* 文本框聚焦状态 */
.record-textarea :deep(.el-textarea__inner:focus) {
  border-color: #1678FF;

}

/* 文本框占位符样式 */
.record-textarea :deep(.el-textarea__inner::placeholder) {
  color: #C1C7D0;
  font-family: 'PingFang SC', sans-serif;
}

/* 底部按钮区域 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  flex-shrink: 0;
}

.delete-btn {
  width: 80px;
  height: 36px;
  background: #FFFFFF;
  border: 1px solid #FF5630;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #FF5630;
  line-height: 20px;
}

.delete-btn:hover {
  background: #FFF2F0;
  border-color: #FF5630;
  color: #FF5630;
}

.save-btn {
  width: 80px;
  height: 36px;
  background: #1678FF;
  border: 1px solid #1678FF;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 20px;
}

.save-btn:hover {
  background: #0052CC;
  border-color: #0052CC;
}
</style>
