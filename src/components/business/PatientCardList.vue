<template>
  <div class="patient-card-list">

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <div class="loading-text">正在加载患者数据...</div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="hasError" class="error-container">
      <el-empty 
        image="/images/error.svg" 
        description="数据加载失败"
      >
        <template #description>
          <div class="error-message">{{ error }}</div>
        </template>
        <el-button type="primary" @click="handleRetry">重试</el-button>
      </el-empty>
    </div>


    <!-- 患者卡片网格 -->
    <div v-else class="card-grid">
      <PatientCard
        v-for="patient in patients"
        :key="patient.id || patient.medicalRecordNo"
        :patient="patient"
        @click="handlePatientClick"
        @view-detail="handleViewDetail"
      />
    </div>
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia'
import { ElMessage } from 'element-plus'
import PatientCard from './PatientCard.vue'
import { usePatientStore } from '@/stores'

// 开发环境导入响应式测试工具
if (import.meta.env.DEV) {
  import('@/utils/responsiveTest.js')
}

// 定义组件props（暂无需要的props）

// 定义组件事件
const emit = defineEmits(['patient-click', 'view-detail', 'refresh'])

// 使用Store
const patientStore = usePatientStore()

// 从Store中获取响应式状态
const {
  patients,
  loading,
  error,
  hasError
} = storeToRefs(patientStore)

// 方法
const handlePatientClick = (patient) => {
  console.log('PatientCardList - 患者卡片点击:', patient)
  console.log('PatientCardList - 患者visitSn:', patient.visitSn, '类型:', typeof patient.visitSn)
  emit('patient-click', patient)
}

const handleViewDetail = (patient) => {
  console.log('查看患者详情:', patient)
  emit('view-detail', patient)
}



const handleRetry = async () => {
  if (currentDepartmentId.value) {
    try {
      await patientStore.fetchPatientsByDepartment(currentDepartmentId.value, true)
      ElMessage.success('重试成功')
    } catch (error) {
      console.error('重试失败:', error)
      ElMessage.error('重试失败，请检查网络连接')
    }
  }
}
</script>

<style scoped>
.patient-card-list {
  width: 100%;
  background-color: #F4F5F7;
  min-height: 400px;
  /* 移除滚动条隐藏，让父容器处理滚动 */
}



/* 卡片网格布局 - 默认单列，固定400px卡片尺寸 */
.card-grid {
  display: grid;
  grid-template-columns: 400px; /* 固定400px宽度 */
  gap: 20px;
  justify-content: center; /* 居中对齐 */
  max-width: 100%;
  overflow-x: hidden; /* 防止水平滚动 */
}

/* 加载状态 */
.loading-container {
  text-align: center;
  padding: 40px 20px;
}

.loading-text {
  margin-top: 16px;
  font-size: 14px;
  color: #6B778C;
  font-family: 'PingFang SC', sans-serif;
}

/* 错误状态 */
.error-container {
  padding: 40px 20px;
  text-align: center;
}

.error-message {
  color: #F56C6C;
  font-size: 14px;
  margin-bottom: 16px;
  font-family: 'PingFang SC', sans-serif;
}

/* 空数据状态 */
.empty-container {
  padding: 40px 20px;
  text-align: center;
}

.empty-message {
  color: #6B778C;
  font-size: 14px;
  font-family: 'PingFang SC', sans-serif;
}



/* 响应式设计 - 基于实际可用空间计算，考虑固定导航栏布局 */

/* 2列布局：需要最小可用内容宽度 840px (800px卡片 + 20px间距 + 20px padding) */
@media screen and (min-width: 1100px) {
  .card-grid {
    grid-template-columns: repeat(2, 400px);
    justify-content: center;
    gap: 20px;
    max-width: calc(2 * 400px + 1 * 20px); /* 820px */
  }
}

/* 3列布局：需要最小可用内容宽度 1260px (1200px卡片 + 40px间距 + 20px padding) */
@media screen and (min-width: 1520px) {
  .card-grid {
    grid-template-columns: repeat(3, 400px);
    justify-content: center;
    gap: 20px;
    max-width: calc(3 * 400px + 2 * 20px); /* 1240px */
  }
}

/* 4列布局：针对1920px分辨率优化 */
/* 可用内容宽度 = 1920px - 220px(侧边栏) - 40px(padding) = 1660px */
/* 考虑滚动条宽度约8px，实际可用约1652px */
@media screen and (min-width: 1920px) {
  .card-grid {
    grid-template-columns: repeat(4, 400px);
    justify-content: flex-start;
    gap: 16px;
    max-width: calc(4 * 400px + 3 * 20px); /* 1660px */
  }

  /* 当内容区域有滚动条时，稍微调整间距 */
  .content-area::-webkit-scrollbar ~ .patient-card-list .card-grid {
    gap: 18px;
    max-width: calc(4 * 400px + 3 * 18px); /* 1654px */
  }
}

/* 移动端优化 - 保持400px卡片尺寸 */
@media screen and (max-width: 768px) {
  .patient-card-list {
    padding: 16px;
  }

  .card-grid {
    grid-template-columns: 400px; /* 固定400px，单列居中 */
    justify-content: center;
    gap: 12px;
  }
}

/* 平板端优化 - 保持400px卡片尺寸 */
@media screen and (min-width: 769px) and (max-width: 1079px) {
  .card-grid {
    grid-template-columns: 400px; /* 固定400px，单列居中 */
    justify-content: center;
    gap: 16px;
  }
}
</style>
