<template>
  <div class="menu-section">
    <!-- 加载状态 -->
    <LoadingState v-if="loading" />

    <!-- 错误状态 -->
    <ErrorState v-else-if="error" :error="error" @retry="handleRetry" />

    <!-- 科室列表 -->
    <div v-else class="menu-list">
      <DepartmentItem
        v-for="department in departments"
        :key="department.id"
        :department="department"
        :selected="selectedDepartmentId === department.id"
        @select="handleSelectDepartment"
      />
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import LoadingState from '@/components/common/LoadingState.vue'
import ErrorState from '@/components/common/ErrorState.vue'
import DepartmentItem from '@/components/business/DepartmentItem.vue'
import { useDepartmentStore } from '@/stores'

// 使用Pinia状态管理
const departmentStore = useDepartmentStore()

// 从store中获取响应式状态
const {
  departments,
  selectedDepartmentId,
  loading,
  error,
  hasData,
  initialized
} = storeToRefs(departmentStore)

// 从store中获取方法
const { fetchDepartments, selectDepartment } = departmentStore

// 重试方法（用于ErrorState组件）
const handleRetry = async () => {
  try {
    await fetchDepartments(true) // 强制刷新
    console.log('科室数据重试加载成功')
  } catch (err) {
    console.error('科室数据重试加载失败:', err)
  }
}

// 处理科室选择（用于DepartmentItem组件）
const handleSelectDepartment = (departmentId) => {
  console.log(`DepartmentMenu: 选择科室 ${departmentId}`)
  selectDepartment(departmentId)
}

// 组件挂载时获取数据（如果还未初始化）
onMounted(async () => {
  if (!initialized.value) {
    try {
      await fetchDepartments()
      console.log('DepartmentMenu: 科室数据初始化完成')
    } catch (err) {
      console.error('DepartmentMenu: 科室数据初始化失败:', err)
    }
  } else {
    console.log('DepartmentMenu: 使用已缓存的科室数据')
  }
})
</script>

<style scoped>
.menu-section {
  flex: 1;
  padding: 0px 0px 8px  8px;
  overflow-y: auto;
}

.menu-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .menu-section {
    flex: 1;
    padding: 8px 0;
    overflow-x: auto;
  }

  .menu-list {
    flex-direction: row;
    gap: 8px;
    min-width: max-content;
  }
}
</style>
