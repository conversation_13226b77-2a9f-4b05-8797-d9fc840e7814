<template>
  <div class="patient-card" @click="handleCardClick">
    <!-- 患者头像和基本信息 -->
    <div class="patient-header">
      <div class="patient-avatar">
        <PatientAvatar :patient="patient" :size="60" />
      </div>
      <div class="patient-basic">
        <div class="patient-name-row">
          <span class="patient-name">{{ patient.patientName || '' }}</span>
          <span class="bed-info">{{ patient.bedNo || '' }}</span>
        </div>
        <div class="gender-age">
          {{  formatGenderAge(patient.gender, patient.patientAge) }}
        </div>
      </div>
    </div>

    <!-- 诊断信息 -->
    <div class="diagnosis-section">
      <span class="diagnosis-label">入院诊断：</span>
      <span class="diagnosis-content">{{ patient.admissionDiagnosis || '' }}</span>
    </div>

    <!-- 详细信息网格 -->
    <div class="info-grid">
      <div class="info-item">
        <span class="info-label">病历号：</span>
        <span class="info-value">{{ patient.medicalRecordNo || '' }}</span>
      </div>
      <div class="info-item info-item-right">
        <span class="info-label">住院次数：</span>
        <span class="info-value">{{ patient.hospitalizationCount || ''  }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">入院时间：</span>
        <span class="info-value">{{ patient.admissionTime }}</span>
      </div>
      <div class="info-item info-item-right">
        <span class="info-label">住院天数：</span>
        <span class="info-value">{{ patient.hospitalizationDays || '' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">管床医生：</span>
        <span class="info-value">{{ patient.attendingPhysician || '' }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { formatGenderAge } from '@/utils/patientUtils'
import PatientAvatar from '@/components/common/PatientAvatar.vue'

// 定义组件props
const props = defineProps({
  patient: {
    type: Object,
    required: true,
    validator: (value) => {
      return value && typeof value === 'object'
    }
  }
})

// 定义组件事件
const emit = defineEmits(['click', 'view-detail'])

// 处理卡片点击
const handleCardClick = () => {
  emit('click', props.patient)
}

</script>

<style scoped>
.patient-card {
  width: 400px;
  height: 232px;
  background: var(--color-bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border-light);
  cursor: pointer;
  transition: var(--transition-slow);
  display: flex;
  flex-direction: column;
  font-family: var(--font-family-primary);
}

.patient-card:hover {
  box-shadow: var(--shadow-bl);
  transform: translateY(-2px);
}

/* 患者头像和基本信息 */
.patient-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.patient-avatar {
  margin-right: var(--spacing-md);
  flex-shrink: 0;
}

.patient-basic {
  flex: 1;
  min-width: 0;
}

.patient-name-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
  text-align: left;
}

.patient-name {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-lg);
  color: var(--color-text-primary);
  line-height: var(--spacing-xl);
  text-align: left;
  font-style: normal;
  text-transform: none;
  flex: 1;
}

.bed-info {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-lg);
  color: var(--color-primary);
  line-height: var(--spacing-xl);
  text-align: right;
  font-style: normal;
  text-transform: none;
  flex-shrink: 0;
  margin-left: var(--spacing-sm);
}

.gender-age {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  line-height: var(--spacing-xl);
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: var(--spacing-xs);
}

/* 诊断信息 */
.diagnosis-section {
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--color-border-light);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  text-align: left;
}

.diagnosis-label {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-md);
  color: var(--color-text-secondary);
  line-height: var(--spacing-xl);
  text-align: left;
  font-style: normal;
  text-transform: none;
  flex-shrink: 0;
}

.diagnosis-content {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-md);
  color: var(--color-text-primary);
  line-height: var(--spacing-xl);
  text-align: left;
  font-style: normal;
  text-transform: none;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 详细信息网格 */
.info-grid {
  position: relative;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-sm);
  flex: 1;
  text-align: left;
}

.info-item {
  display: flex;
  align-items: center;
  min-height: var(--height-xs);
  text-align: left;
}

.info-item-right {
  margin-left: 70px;
}

.info-label {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-md);
  color: var(--color-text-secondary);
  line-height: var(--spacing-xl);
  text-align: justify;
  text-align-last: justify;
  font-style: normal;
  text-transform: none;
  margin-right: var(--spacing-xs);
  flex-shrink: 0;
  width: 80px;
}

.info-value {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-md);
  color: var(--color-text-primary);
  line-height: var(--spacing-xl);
  text-align: left;
  font-style: normal;
  text-transform: none;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 移除响应式卡片尺寸变化 - 始终保持400px×232px固定尺寸 */
/* 响应式适配通过PatientCardList.vue中的grid布局列数调整实现 */

/* 加载状态和错误状态样式 */
.patient-card.loading {
  opacity: 0.6;
  pointer-events: none;
}

.patient-card.error {
  border-color: var(--color-error);
  background-color: var(--color-bg-tertiary);
}

.patient-card.error .patient-name {
  color: var(--color-error);
}
</style>
