<template>
  <div
    class="menu-item"
    :class="{ 'menu-item-selected': selected }"
    @click="handleSelect"
  >
    <span class="department-name">{{ department.name }}</span>
    <span class="patient-count">{{ department.patientCount }}人</span>
  </div>
</template>

<script setup>
// 定义组件props
const props = defineProps({
  department: {
    type: Object,
    required: true,
    validator: (value) => {
      return value && typeof value.id !== 'undefined' && typeof value.name === 'string'
    }
  },
  selected: {
    type: Boolean,
    default: false
  }
})

// 定义组件事件
const emit = defineEmits(['select'])

// 处理选择事件
const handleSelect = () => {
  emit('select', props.department.id)
}
</script>

<style scoped>
/* 菜单项样式 */
.menu-item {
  width: 100%;
  height: 44px;
  border-radius: var(--border-radius-xs);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px 0 8px;
  cursor: pointer;
  transition: var(--transition-base);
  background: transparent;
  border: none;
}

/* 菜单项选中状态 */
.menu-item-selected {
  background: var(--color-bg-primary);
  border-left: 2px solid var(--color-primary);
}

/* 菜单项悬停效果 */
.menu-item:hover:not(.menu-item-selected) {
  background: var(--color-primary-hover);
  border: 1px solid var(--color-primary-active);
}

/* 科室名称样式 */
.department-name {
  width: 112px;
  height: var(--spacing-lg);
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-md);
  color: var(--color-text-primary);
  line-height: var(--spacing-lg);
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 选中状态的科室名称样式 */
.menu-item-selected .department-name {
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
}

/* 患者人数样式 */
.patient-count {
  width: 26px;
  height: var(--spacing-lg);
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  line-height: var(--spacing-lg);
  text-align: right;
  white-space: nowrap;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .menu-item {
    width: 120px;
    height: 32px;
    font-size: 12px;
  }

  .department-name {
    font-size: 12px;
    width: auto;
  }

  .patient-count {
    font-size: 10px;
    width: auto;
  }
}
</style>
