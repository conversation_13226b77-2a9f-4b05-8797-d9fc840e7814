<template>
  <div class="logo-section">
    <div class="logo-container">
      <div class="logo-icon">
        <img src="@/assets/images/logo.png" alt="测试医院Logo" class="logo-image" />
      </div>
      <div class="system-name">测试医院</div>
    </div>
  </div>
</template>

<script setup>
// Logo 组件不需要额外的逻辑
</script>

<style scoped>
.logo-section {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 26px;
  height: 39px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-image {
  width: 26px;
  height: 39px;
  object-fit: contain;
  display: block;
}

.system-name {
  width: 88px;
  height: 20px;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 600;
  font-size: 22px;
  color: #172B4D;
  line-height: 20px;
  text-align: center;
  white-space: nowrap;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .logo-section {
    height: 60px;
    min-width: 150px;
  }
  
  .system-name {
    font-size: 16px;
    width: auto;
  }
}
</style>
