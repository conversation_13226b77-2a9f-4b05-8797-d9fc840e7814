<template>
  <div class="discharge-record-container">
    <!-- 左侧患者信息详情面板 -->
    <PatientInfoPanel :patient="patient" />

    <!-- 右侧出院记录内容区域 -->
    <div class="record-content-area">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container" v-loading="loading" element-loading-text="正在加载出院记录...">
        <div style="height: 200px;"></div>
      </div>

      <!-- 内容区域 -->
      <div v-else>
        <!-- 页面标题、时间信息和按钮行 -->
        <div class="page-header">
          <h2 class="title-text">出院记录</h2>
          <div class="header-actions">
            <div class="time-info-section">
              <div class="time-item">
                <span class="time-label">创建时间：</span>
                <span class="time-value">{{ recordData.recordDatetime }}</span>
              </div>
              <div class="time-item">
                <span class="time-label">更新时间：</span>
                <span class="time-value">{{ recordData.recordUpdateDatetime }}</span>
              </div>
            </div>
            <div class="form-actions">
              <el-button
                class="delete-btn"
                @click="handleDelete"
                :loading="deleteLoading"
                :disabled="!recordData.recordSn"
              >
                删除
              </el-button>
              <el-button
                type="primary"
                class="save-btn"
                @click="handleSave"
                :loading="saveLoading"
              >
                保存
              </el-button>
            </div>
          </div>
        </div>


        <!-- 出院记录表单 -->
        <div class="record-form">
          <!-- 诊断信息行 -->
          <div class="form-item-row">
            <!-- 入院诊断 -->
            <div class="form-item-half">
              <label class="form-label">入院诊断：</label>
              <el-input
                v-model="formData.admissionDiag"
                type="textarea"
                placeholder="请输点击选择诊断信息"
                :rows="2"
                class="form-textarea small diagnosis-clickable"
                @click="handleAdmissionDiagnosisClick"
              />
            </div>

            <!-- 出院诊断 -->
            <div class="form-item-half">
              <label class="form-label">出院诊断：</label>
              <el-input
                v-model="formData.dischargeDiag"
                type="textarea"
                placeholder="请输点击选择诊断信息"
                :rows="2"
                class="form-textarea small diagnosis-clickable"
                @click="handleDischargeDiagnosisClick"
              />
            </div>
          </div>

          <!-- 入院情况 -->
          <div class="form-item">
            <label class="form-label">入院情况：</label>
            <el-input
              ref="admissionConditionRef"
              v-model="formData.admissionCondition"
              type="textarea"
              placeholder="请输入内容"
              class="form-textarea autosize-admission-condition"
              @input="handleAdmissionConditionInput"
            />
          </div>

          <!-- 诊疗经过 -->
          <div class="form-item">
            <label class="form-label">诊疗经过：</label>
            <el-input
              ref="treatmentInfoRef"
              v-model="formData.treatmentInfo"
              type="textarea"
              placeholder="请输入内容"
              class="form-textarea autosize-treatment-info"
              @input="handleTreatmentInfoInput"
            />
          </div>

          <!-- 出院情况 -->
          <div class="form-item">
            <label class="form-label">出院情况：</label>
            <el-input
              v-model="formData.dischargeCondition"
              type="textarea"
              placeholder="请输入内容"
              :rows="2"
              class="form-textarea small"
            />
          </div>

          <!-- 出院医嘱 -->
          <div class="form-item">
            <label class="form-label">出院医嘱：</label>
            <el-input
              v-model="formData.dischargeOrder"
              type="textarea"
              placeholder="请输入内容"
              :rows="3"
              class="form-textarea medium"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 入院诊断信息选择对话框 -->
    <DiagnosisSelector
      v-model="showAdmissionDiagnosisSelector"
      :patient="patient"
      :diag-type="'入院诊断'"
      @confirm="handleAdmissionDiagnosisConfirm"
    />

    <!-- 出院诊断信息选择对话框 -->
    <DiagnosisSelector
      v-model="showDischargeDiagnosisSelector"
      :patient="patient"
      :diag-type="'出院诊断'"
      @confirm="handleDischargeDiagnosisConfirm"
    />

    <!-- 质控预警弹窗 -->
    <div v-if="showQualityControlDialog" class="quality-control-dialog-overlay" @click="handleQualityControlDialogCancel">
      <div class="quality-control-dialog-container" @click.stop>
        <div class="quality-control-dialog-content">
          <div class="quality-control-dialog-title">
            <img src="@/assets/images/note.png" alt="注意" class="note-icon" />
            <p class="title-text">请注意！</p>
          </div>
          <div class="quality-control-dialog-message">
            <p class="message-text">
              该患者在本次就诊过程中存在{{ qualityControlErrors.length }}个不规范的行为。详细点击
              <span class="action-link" @click="handleQualityControlCheck">质控预警查看</span>
            </p>
          </div>
          <div class="quality-control-dialog-actions">
            <span class="action-link return-link" @click="handleQualityControlDialogCancel">返回病历</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { apiServices } from '@/api'
import PatientInfoPanel from './PatientInfoPanel.vue'
import DiagnosisSelector from './DiagnosisSelector.vue'

// 定义组件props
const props = defineProps({
  patient: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

// 定义组件事件
const emit = defineEmits(['patient-data-updated'])

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const deleteLoading = ref(false)
const showAdmissionDiagnosisSelector = ref(false)
const showDischargeDiagnosisSelector = ref(false)

// 文本框引用
const admissionConditionRef = ref(null)
const treatmentInfoRef = ref(null)

// 质控预警弹窗相关
const showQualityControlDialog = ref(false)
const qualityControlErrors = ref([])

// 出院记录数据
const recordData = reactive({
  recordDatetime: '',
  recordUpdateDatetime: '',
  recordSn: '',
  visitSn: ''
})

// 表单数据
const formData = reactive({
  admissionDiag: '',
  dischargeDiag: '',
  admissionCondition: '',
  treatmentInfo: '',
  dischargeCondition: '',
  dischargeOrder: ''
})

// 加载出院记录数据
const loadDischargeRecord = async () => {
  if (!props.patient?.visitSn) {
    return
  }

  try {
    loading.value = true

    const data = await apiServices.dischargeRecord.getDetail(props.patient.visitSn)

    // 更新记录数据
    Object.assign(recordData, {
      recordDatetime: data.recordDatetime || '',
      recordUpdateDatetime: data.recordUpdateDatetime || '',
      recordSn: data.recordSn || '',
      visitSn: data.visitSn || props.patient.visitSn
    })

    // 更新表单数据
    Object.assign(formData, {
      admissionDiag: data.admissionDiag || '',
      dischargeDiag: data.dischargeDiag || '',
      admissionCondition: data.admissionCondition || '',
      treatmentInfo: data.treatmentInfo || '',
      dischargeCondition: data.dischargeCondition || '',
      dischargeOrder: data.dischargeOrder || ''
    })


    // 强制初始化文本框高度
    setTimeout(() => {
      forceInitTextareaHeight()
    }, 100)
  } catch (error) {
    ElMessage.error('加载出院记录失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 保存出院记录
const handleSave = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法保存')
    return
  }

  try {
    saveLoading.value = true

    // 构建保存数据
    const saveData = {
      visitSn: props.patient.visitSn,
      recordSn: recordData.recordSn,
      ...formData
    }


    const result = await apiServices.dischargeRecord.save(saveData)

    // 更新记录信息
    if (result.recordSn) {
      recordData.recordSn = result.recordSn
    }
    if (result.recordDatetime) {
      recordData.recordDatetime = result.recordDatetime
    }
    if (result.recordUpdateDatetime) {
      recordData.recordUpdateDatetime = result.recordUpdateDatetime
    }

    ElMessage.success('保存成功')

    // 通知父组件患者数据已更新，需要重新加载
    emit('patient-data-updated')
  } catch (error) {

    // 检查是否是质控预警相关的错误（code为1001）
    if (error.code === 1001) {
      // 从错误数据中获取质控错误列表
      // error.data是完整的响应对象，真正的数组在error.data.data中
      let errorList = null
      if (error.data && error.data.data && Array.isArray(error.data.data)) {
        errorList = error.data.data
      } else if (error.data && Array.isArray(error.data)) {
        errorList = error.data
      }

      if (errorList && errorList.length > 0) {
        qualityControlErrors.value = errorList
      } else {
        // 如果没有具体的错误列表，使用默认值
        qualityControlErrors.value = ['出院记录推送失败']
      }

      // 显示质控预警弹窗
      showQualityControlDialog.value = true
    } else {
      ElMessage.error('保存失败: ' + error.message)
    }
  } finally {
    saveLoading.value = false
  }
}

// 删除出院记录
const handleDelete = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确定要删除这条出院记录吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    deleteLoading.value = true


    await apiServices.dischargeRecord.delete(props.patient.visitSn)

    // 清空表单数据
    Object.assign(formData, {
      admissionDiag: '',
      dischargeDiag: '',
      admissionCondition: '',
      treatmentInfo: '',
      dischargeCondition: '',
      dischargeOrder: ''
    })

    // 清空记录数据
    Object.assign(recordData, {
      recordDatetime: '',
      recordUpdateDatetime: '',
      recordSn: '',
      visitSn: props.patient.visitSn
    })

    ElMessage.success('删除成功')

    // 通知父组件患者数据已更新，需要重新加载
    emit('patient-data-updated')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  } finally {
    deleteLoading.value = false
  }
}

// 监听患者变化，重新加载数据
watch(() => props.patient?.visitSn, (newVisitSn) => {
  if (newVisitSn) {
    loadDischargeRecord()
  }
}, { immediate: false })

// 监听入院情况内容变化，自动调整高度
watch(() => formData.admissionCondition, (newValue, oldValue) => {
  // 只有在值真正改变时才调整高度，避免无限循环
  if (newValue !== oldValue) {
    nextTick(() => {
      nextTick(() => {
        setTimeout(() => {
          adjustAdmissionConditionHeight()
        }, 10) // 短延迟确保DOM更新完成
      })
    })
  }
}, { flush: 'post' })

// 监听诊疗经过内容变化，自动调整高度
watch(() => formData.treatmentInfo, (newValue, oldValue) => {
  // 只有在值真正改变时才调整高度，避免无限循环
  if (newValue !== oldValue) {
    nextTick(() => {
      nextTick(() => {
        setTimeout(() => {
          adjustTreatmentInfoHeight()
        }, 10) // 短延迟确保DOM更新完成
      })
    })
  }
}, { flush: 'post' })

// 组件挂载时加载数据
onMounted(() => {
  if (props.patient?.visitSn) {
    loadDischargeRecord()
  } else {
    // 即使没有数据也要初始化文本框高度
    nextTick(() => {
      nextTick(() => {
        adjustAdmissionConditionHeight()
        adjustTreatmentInfoHeight()
      })
    })
  }
})

// ========== 文本框自适应高度和滚动优化 ==========

/**
 * 处理入院情况输入变化
 */
const handleAdmissionConditionInput = () => {
  nextTick(() => {
    adjustAdmissionConditionHeight()
  })
}

/**
 * 处理诊疗经过输入变化
 */
const handleTreatmentInfoInput = () => {
  nextTick(() => {
    adjustTreatmentInfoHeight()
  })
}

/**
 * 强制初始化文本框高度（用于数据加载后）
 */
const forceInitTextareaHeight = () => {
  // 多次尝试，确保成功
  let attempts = 0
  const maxAttempts = 5

  const tryAdjust = () => {
    attempts++

    if (admissionConditionRef.value && treatmentInfoRef.value) {
      adjustAdmissionConditionHeight()
      adjustTreatmentInfoHeight()
    } else if (attempts < maxAttempts) {
      setTimeout(tryAdjust, 100)
    }
  }

  tryAdjust()
}

/**
 * 动态调整入院情况文本框高度
 */
const adjustAdmissionConditionHeight = () => {
  if (!admissionConditionRef.value) {
    return
  }

  try {
    const textareaElement = admissionConditionRef.value.$el.querySelector('.el-textarea__inner')
    if (!textareaElement) {
      return
    }

    // 确保文本内容已经设置到DOM中
    const currentValue = formData.admissionCondition || ''
    if (textareaElement.value !== currentValue) {
      textareaElement.value = currentValue
    }

    // 临时设置高度为auto来获取内容的真实高度
    textareaElement.style.height = 'auto'

    // 强制重新计算布局
    textareaElement.offsetHeight

    // 获取内容的滚动高度
    const scrollHeight = textareaElement.scrollHeight

    // 计算最小高度（2行）
    const computedStyle = window.getComputedStyle(textareaElement)
    const fontSize = parseFloat(computedStyle.fontSize) || 14
    const lineHeight = parseFloat(computedStyle.lineHeight) || fontSize * 1.6
    const paddingTop = parseFloat(computedStyle.paddingTop) || 12
    const paddingBottom = parseFloat(computedStyle.paddingBottom) || 12
    const borderTop = parseFloat(computedStyle.borderTopWidth) || 1
    const borderBottom = parseFloat(computedStyle.borderBottomWidth) || 1

    // 2行的最小高度
    const minHeight = lineHeight * 2 + paddingTop + paddingBottom + borderTop + borderBottom

    // 计算最终高度：内容高度和最小高度的较大值，并添加一些缓冲
    const finalHeight = Math.max(scrollHeight + 2, minHeight) // +2px 缓冲

    // 设置新的高度
    textareaElement.style.height = finalHeight + 'px'

  } catch (error) {
  }
}

/**
 * 动态调整诊疗经过文本框高度
 */
const adjustTreatmentInfoHeight = () => {
  if (!treatmentInfoRef.value) {
    return
  }

  try {
    const textareaElement = treatmentInfoRef.value.$el.querySelector('.el-textarea__inner')
    if (!textareaElement) {
      return
    }

    // 确保文本内容已经设置到DOM中
    const currentValue = formData.treatmentInfo || ''
    if (textareaElement.value !== currentValue) {
      textareaElement.value = currentValue
    }

    // 临时设置高度为auto来获取内容的真实高度
    textareaElement.style.height = 'auto'

    // 强制重新计算布局
    textareaElement.offsetHeight

    // 获取内容的滚动高度
    const scrollHeight = textareaElement.scrollHeight

    // 计算最小高度（3行）
    const computedStyle = window.getComputedStyle(textareaElement)
    const fontSize = parseFloat(computedStyle.fontSize) || 14
    const lineHeight = parseFloat(computedStyle.lineHeight) || fontSize * 1.6
    const paddingTop = parseFloat(computedStyle.paddingTop) || 12
    const paddingBottom = parseFloat(computedStyle.paddingBottom) || 12
    const borderTop = parseFloat(computedStyle.borderTopWidth) || 1
    const borderBottom = parseFloat(computedStyle.borderBottomWidth) || 1

    // 3行的最小高度
    const minHeight = lineHeight * 3 + paddingTop + paddingBottom + borderTop + borderBottom

    // 计算最终高度：内容高度和最小高度的较大值，并添加一些缓冲
    const finalHeight = Math.max(scrollHeight + 2, minHeight) // +2px 缓冲

    // 设置新的高度
    textareaElement.style.height = finalHeight + 'px'

  } catch (error) {
  }
}



// ========== 诊断信息选择相关方法 ==========

/**
 * 处理入院诊断输入框点击
 */
const handleAdmissionDiagnosisClick = () => {
  showAdmissionDiagnosisSelector.value = true
}

/**
 * 处理出院诊断输入框点击
 */
const handleDischargeDiagnosisClick = () => {
  showDischargeDiagnosisSelector.value = true
}

/**
 * 处理入院诊断信息确认选择
 */
const handleAdmissionDiagnosisConfirm = (diagnosisText) => {

  // 将拼接的诊断信息字符串填入输入框
  if (diagnosisText) {
    formData.admissionDiag = diagnosisText
    ElMessage.success('入院诊断信息选择成功')
  }
}

/**
 * 处理出院诊断信息确认选择
 */
const handleDischargeDiagnosisConfirm = (diagnosisText) => {

  // 将拼接的诊断信息字符串填入输入框
  if (diagnosisText) {
    formData.dischargeDiag = diagnosisText
    ElMessage.success('出院诊断信息选择成功')
  }
}

// ========== 质控预警弹窗处理方法 ==========

/**
 * 处理质控预警查看
 */
const handleQualityControlCheck = () => {
  // 暂时不做任何处理，只是一个按钮
  // ElMessage.info('质控预警查看功能暂未实现')
}

/**
 * 处理取消质控预警弹窗（返回病历）
 */
const handleQualityControlDialogCancel = () => {
  // 关闭弹窗
  showQualityControlDialog.value = false

  // 清空质控错误数据
  qualityControlErrors.value = []
}
</script>

<style scoped>
/* 整体容器 */
.discharge-record-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 180px); /* 使用视口高度减去导航栏高度，确保一屏显示 */
  gap: 20px;
}

/* 右侧记录内容区域 */
.record-content-area {
  flex: 1;
  height: 100%; /* 使用100%高度，与容器保持一致 */
  background: #FFFFFF;
  border-radius: 0 12px 12px 0;
  padding: 14px; /* 减少内边距以适配更多内容 */
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 添加垂直滚动，以防内容过多 */
}

/* 加载容器 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

/* 页面标题、时间信息和按钮行 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0; /* 防止压缩 */
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 40px;
}

.title-text {
  width: auto; /* 自动宽度 */
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: #172B4D;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin: 0; /* 移除默认margin */
}

/* 时间信息头部 */
.time-info-section {
  display: flex;
  gap: 40px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-label {
  width: 80px;
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #6B778C;
  line-height: 20px;
  text-align: left;
}

.time-value {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #172B4D;
  line-height: 20px;
}

/* 表单区域 */
.record-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px; /* 减少表单项之间的间距以适配一页显示 */
  min-height: 0; /* 允许内容压缩 */
}

.form-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

/* 两列表单行样式 */
.form-item-row {
  display: flex;
  gap: 16px; /* 减少列间距 */
  width: 100%;
}

.form-item-half {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
  /* 设置最小宽度以确保标签和输入框有足够空间 */
  min-width: calc(130px + 12px + 200px); /* 标签宽度 + 间距 + 最小输入框宽度 */
}

.form-label {
  width: 130px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #172B4D;
  line-height: 20px;
  text-align: right;
  margin-top: 8px;
  flex-shrink: 0;
}

/* 确保两列布局中的标签与单列布局保持一致的对齐 */
.form-item-half .form-label {
  width: 130px; /* 与单列布局保持相同宽度，确保冒号垂直对齐 */
}

/* 响应式布局 - 当空间不足时改为单列 */
@media (max-width: 1200px) {
  .form-item-row {
    flex-direction: column;
    gap: 20px;
  }

  .form-item-half {
    min-width: auto; /* 移除最小宽度限制 */
  }

  .form-item-half .form-label {
    width: 130px; /* 保持标签宽度一致 */
  }
}

/* 文本框样式 */
.form-textarea {
  width: 100%;
}

.form-textarea.small :deep(.el-textarea__inner) {
  height: 55px; /* 进一步减少高度，适配紧凑布局 */
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #EBECF0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  resize: none;
  line-height: 1.4; /* 调整行高 */
}

.form-textarea.medium :deep(.el-textarea__inner) {
  height: 70px; /* 进一步减少高度，适配紧凑布局 */
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #EBECF0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  resize: none;
  line-height: 1.4; /* 调整行高 */
}

.form-textarea.large :deep(.el-textarea__inner) {
  height: 90px; /* 大文本框高度 */
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #EBECF0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  resize: none;
  line-height: 1.4; /* 调整行高 */
}

/* 入院情况自适应高度文本框专用样式 - 使用高优先级选择器 */
.discharge-record-container .record-content-area .form-textarea.autosize-admission-condition :deep(.el-textarea__inner) {
  background: #FFFFFF !important;
  border-radius: 4px !important;
  border: 1px solid #EBECF0 !important;
  font-family: 'PingFang SC', sans-serif !important;
  font-size: 14px !important;
  color: #172B4D !important;
  resize: none !important;
  line-height: 1.6 !important;
  padding: 5px 12px !important;
  box-sizing: border-box !important;
  /* 隐藏滚动条 */
  overflow-y: hidden !important;
  /* 不设置min-height和height，完全由JavaScript动态控制 */
}

/* 诊疗经过自适应高度文本框专用样式 - 使用高优先级选择器 */
.discharge-record-container .record-content-area .form-textarea.autosize-treatment-info :deep(.el-textarea__inner) {
  background: #FFFFFF !important;
  border-radius: 4px !important;
  border: 1px solid #EBECF0 !important;
  font-family: 'PingFang SC', sans-serif !important;
  font-size: 14px !important;
  color: #172B4D !important;
  resize: none !important;
  line-height: 1.6 !important;
  padding: 5px 12px !important;
  box-sizing: border-box !important;
  /* 隐藏滚动条 */
  overflow-y: hidden !important;
  /* 不设置min-height和height，完全由JavaScript动态控制 */
}

/* 文本框聚焦状态 */
.form-textarea :deep(.el-textarea__inner:focus) {
  border-color: #1678FF;
}

/* 入院情况文本框聚焦状态 */
.discharge-record-container .record-content-area .form-textarea.autosize-admission-condition :deep(.el-textarea__inner:focus) {
  border-color: #1678FF !important;
}

/* 诊疗经过文本框聚焦状态 */
.discharge-record-container .record-content-area .form-textarea.autosize-treatment-info :deep(.el-textarea__inner:focus) {
  border-color: #1678FF !important;
}

/* 文本框占位符样式 */
.form-textarea :deep(.el-textarea__inner::placeholder) {
  color: #C1C7D0;
  font-family: 'PingFang SC', sans-serif;
}

/* 底部按钮区域 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  flex-shrink: 0; /* 防止压缩 */
}

.delete-btn {
  width: 80px;
  height: 36px;
  background: #FFFFFF;
  border: 1px solid #FF5630;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #FF5630;
  line-height: 20px;
}

.delete-btn:hover {
  background: #FFF2F0;
  border-color: #FF5630;
  color: #FF5630;
}

.save-btn {
  width: 80px;
  height: 36px;
  background: #1678FF;
  border: 1px solid #1678FF;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 20px;
}

.save-btn:hover {
  background: #0052CC;
  border-color: #0052CC;
}

/* 诊断输入框点击样式 */
.diagnosis-clickable :deep(.el-textarea__inner) {
  cursor: pointer;
}

.diagnosis-clickable :deep(.el-textarea__inner:hover) {
  border-color: #1678FF;
}

/* ========== 质控预警弹窗样式 ========== */

/* 弹窗遮罩层 */
.quality-control-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

/* 弹窗容器 */
.quality-control-dialog-container {
  background: #FFFFFF;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

/* 弹窗内容 */
.quality-control-dialog-content {
  padding: 24px;
  background-color: rgba(255, 251, 230, 1);
  border-color: rgba(255, 229, 143, 1);
}

/* 弹窗标题 */
.quality-control-dialog-title {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.quality-control-dialog-title .note-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  display: block;
}

.quality-control-dialog-title .title-text {
  font-family: 'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-size: 18px;
  line-height: 20px; /* 调整行高与图标高度一致 */
  color: rgba(0, 0, 0, 0.647);
  margin: 0;
  font-weight: 500;
  display: flex;
  align-items: center;
}

/* 弹窗消息 */
.quality-control-dialog-message {
  margin-bottom: 4px;
  margin-left: 28px; /* 图片宽度(20px) + 间距(8px) = 28px，与"请注意"文字对齐 */
}

.quality-control-dialog-message .message-text {
  font-family: 'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-size: 14px;
  line-height: 26px;
  color: rgba(0, 0, 0, 0.447);
  margin: 0;
}

/* 弹窗操作按钮区域 */
.quality-control-dialog-actions {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-left: 28px; /* 图片宽度(20px) + 间距(8px) = 28px，与"请注意"文字对齐 */
}

/* 操作链接 */
.action-link {
  font-family: 'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-size: 14px;
  line-height: 26px;
  color: #108DE9;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.2s ease;
}

.action-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

/* 返回病历链接特殊样式 */
.action-link.return-link {
  color: rgba(0, 178, 255, 0.647);
}

.action-link.return-link:hover {
  color: rgba(0, 178, 255, 0.8);
}
</style>
