<template>
  <div class="medical-record-menu">
    <!-- 病历文书 -->
    <div class="menu-section">
      <div class="menu-item expandable" :class="{ expanded: expandedSections.includes('medical-records') }">
        <div class="item-header" @click="toggleSection('medical-records')">
          <span class="expand-icon" :class="{ rotated: expandedSections.includes('medical-records') }"></span>
          <span class="item-text">病历文书</span>
        </div>
        <div class="sub-items" v-show="expandedSections.includes('medical-records')">
          <!-- 入院记录 -->
          <div
            class="sub-item clickable"
            :class="{ active: selectedItem === 'admission-record' }"
            @click="handleItemClick('admission-record', '入院记录')"
          >
            入院记录
          </div>

          <!-- 病程记录 -->
          <div class="sub-menu-item expandable" :class="{ expanded: expandedSections.includes('progress-records') }">
            <div class="sub-item-header" @click="toggleSection('progress-records')">
              <span class="expand-icon" :class="{ rotated: expandedSections.includes('progress-records') }"></span>
              <span class="sub-item-text">病程记录</span>
            </div>
            <div class="sub-sub-items" v-show="expandedSections.includes('progress-records')">
              <div
                class="sub-sub-item clickable"
                :class="{ active: selectedItem === 'first-progress' }"
                @click="handleItemClick('first-progress', '首次病程记录')"
              >
                首次病程记录
              </div>
              <div
                class="sub-sub-item clickable"
                :class="{ active: selectedItem === 'consultation' }"
                @click="handleItemClick('consultation', '会诊记录')"
              >
                会诊记录
              </div>
              <div
                class="sub-sub-item clickable"
                :class="{ active: selectedItem === 'preop-discussion' }"
                @click="handleItemClick('preop-discussion', '术前讨论记录')"
              >
                术前讨论记录
              </div>
              <div
                class="sub-sub-item clickable"
                :class="{ active: selectedItem === 'invasive-procedure' }"
                @click="handleItemClick('invasive-procedure', '有创诊疗操作记录')"
              >
                有创诊疗操作记录
              </div>
              <div
                class="sub-sub-item clickable"
                :class="{ active: selectedItem === 'preop-summary' }"
                @click="handleItemClick('preop-summary', '术前小结')"
              >
                术前小结
              </div>
              <div
                class="sub-sub-item clickable"
                :class="{ active: selectedItem === 'surgery-record' }"
                @click="handleItemClick('surgery-record', '手术记录')"
              >
                手术记录
              </div>
              <div
                class="sub-sub-item clickable"
                :class="{ active: selectedItem === 'postop-first' }"
                @click="handleItemClick('postop-first', '术后首次病程')"
              >
                术后首次病程
              </div>
              <div
                class="sub-sub-item clickable"
                :class="{ active: selectedItem === 'tnm-staging' }"
                @click="handleItemClick('tnm-staging', 'TNM分期记录')"
              >
                TNM分期记录
              </div>
              <div
                class="sub-sub-item clickable"
                :class="{ active: selectedItem === 'daily-progress' }"
                @click="handleItemClick('daily-progress', '日常病程记录')"
              >
                日常病程记录（{{ dailyProgressCount }}）
              </div>
            </div>
          </div>

          <!-- 出院记录 -->
          <div
            class="sub-item clickable"
            :class="{ active: selectedItem === 'discharge-record' }"
            @click="handleItemClick('discharge-record', '出院记录')"
          >
            出院记录
          </div>
        </div>
      </div>
    </div>

    <!-- 诊疗操作 -->
    <div class="menu-section">
      <div class="menu-item expandable" :class="{ expanded: expandedSections.includes('treatment-operations') }">
        <div class="item-header" @click="toggleSection('treatment-operations')">
          <span class="expand-icon" :class="{ rotated: expandedSections.includes('treatment-operations') }"></span>
          <span class="item-text">诊疗操作</span>
        </div>
        <div class="sub-items" v-show="expandedSections.includes('treatment-operations')">
          <div
            class="sub-item clickable"
            :class="{ active: selectedItem === 'diagnosis-info' }"
            @click="handleItemClick('diagnosis-info', '诊断信息')"
          >
            诊断信息
          </div>

          <div
            class="sub-item clickable"
            :class="{ active: selectedItem === 'medical-orders' }"
            @click="handleItemClick('medical-orders', '医嘱信息')"
          >
            医嘱信息
          </div>
        </div>
      </div>
    </div>

    <!-- 辅助检查 -->
    <div class="menu-section">
      <div class="menu-item expandable" :class="{ expanded: expandedSections.includes('auxiliary-examinations') }">
        <div class="item-header" @click="toggleSection('auxiliary-examinations')">
          <span class="expand-icon" :class="{ rotated: expandedSections.includes('auxiliary-examinations') }"></span>
          <span class="item-text">辅助检查</span>
        </div>
        <div class="sub-items" v-show="expandedSections.includes('auxiliary-examinations')">


          <div
            class="sub-item clickable"
            :class="{ active: selectedItem === 'lab-report' }"
            @click="handleItemClick('lab-report', '检验报告')"
          >
            检验报告（{{ labReportCount }}）
          </div>
          <div
              class="sub-item clickable"
              :class="{ active: selectedItem === 'examination-report' }"
              @click="handleItemClick('examination-report', '检查报告')"
          >
            检查报告（{{ examReportCount }}）
          </div>
          <div
            class="sub-item clickable"
            :class="{ active: selectedItem === 'pathology-report' }"
            @click="handleItemClick('pathology-report', '病理报告')"
          >
            病理报告（{{ pathologyReportCount }}）
          </div>

          <div
            class="sub-item clickable"
            :class="{ active: selectedItem === 'molecular-pathology' }"
            @click="handleItemClick('molecular-pathology', '分子病理报告')"
          >
            分子病理报告（{{ molecularPathologyCount }}）
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { apiServices } from '@/api'

// 定义组件props
const props = defineProps({
  initialSelected: {
    type: String,
    default: 'admission-record'
  },
  patient: {
    type: Object,
    default: () => ({})
  }
})

// 定义组件事件
const emit = defineEmits(['item-select'])

// 响应式数据
const selectedItem = ref(props.initialSelected) // 使用传入的初始选中项
const expandedSections = ref([
  'medical-records',      // 病历文书
  'progress-records',     // 病程记录
  'treatment-operations', // 诊疗操作
  'auxiliary-examinations' // 辅助检查
]) // 默认展开所有菜单

// 数量相关数据
const dailyProgressCount = ref(0)
const diagnosisInfoCount = ref(0)
const medicalOrderCount = ref(0)
const examReportCount = ref(0)
const pathologyReportCount = ref(0)
const molecularPathologyCount = ref(0)
const labReportCount = ref(0)

// 监听props变化
watch(() => props.initialSelected, (newValue) => {
  selectedItem.value = newValue
})

// 监听患者变化，重新加载数量
watch(() => props.patient?.visitSn, (newVisitSn) => {
  if (newVisitSn) {
    loadDailyProgressCount()
    loadDiagnosisInfoCount()
    loadMedicalOrderCount()
    loadExamReportCount()
    loadPathologyReportCount()
    loadMolecularPathologyCount()
    loadLabReportCount()
  }
}, { immediate: false })

// 处理菜单项点击
const handleItemClick = (itemKey, itemName) => {
  selectedItem.value = itemKey
  emit('item-select', { key: itemKey, name: itemName })
}

// 切换展开/收起状态
const toggleSection = (sectionKey) => {
  const index = expandedSections.value.indexOf(sectionKey)
  if (index > -1) {
    expandedSections.value.splice(index, 1)
  } else {
    expandedSections.value.push(sectionKey)
  }
}

// 菜单项名称映射
const getMenuItemName = (key) => {
  const nameMap = {
    'admission-record': '入院记录',
    'first-progress': '首次病程记录',
    'consultation': '会诊记录',
    'preop-discussion': '术前讨论记录',
    'invasive-procedure': '有创诊疗操作记录',
    'preop-summary': '术前小结',
    'surgery-record': '手术记录',
    'postop-first': '术后首次病程',
    'tnm-staging': 'TNM分期记录',
    'daily-progress': '日常病程记录',
    'discharge-record': '出院记录',
    'diagnosis-info': '诊断信息',
    'medical-orders': '医嘱信息',
    'examination-report': '检查报告',
    'lab-report': '检验报告',
    'pathology-report': '病理报告',
    'molecular-pathology': '分子病理报告'
  }
  return nameMap[key] || key
}

// ========== 数量加载方法 ==========

/**
 * 加载日常病程记录数量
 */
const loadDailyProgressCount = async () => {
  if (!props.patient?.visitSn) {
    dailyProgressCount.value = 0
    return
  }

  try {
    const count = await apiServices.dailyProgressRecord.getCount(props.patient.visitSn)
    dailyProgressCount.value = count
  } catch (error) {
    dailyProgressCount.value = 0
  }
}

/**
 * 加载诊断信息数量
 */
const loadDiagnosisInfoCount = async () => {
  if (!props.patient?.visitSn) {
    diagnosisInfoCount.value = 0
    return
  }

  try {
    const count = await apiServices.diagnosisInfo.getCount(props.patient.visitSn)
    diagnosisInfoCount.value = count
  } catch (error) {
    diagnosisInfoCount.value = 0
  }
}

/**
 * 加载医嘱信息数量
 */
const loadMedicalOrderCount = async () => {
  if (!props.patient?.visitSn) {
    medicalOrderCount.value = 0
    return
  }

  try {
    const count = await apiServices.medicalOrder.getCount(props.patient.visitSn)
    medicalOrderCount.value = count
  } catch (error) {
    medicalOrderCount.value = 0
  }
}

/**
 * 加载检查报告数量
 */
const loadExamReportCount = async () => {
  if (!props.patient?.visitSn) {
    examReportCount.value = 0
    return
  }

  try {
    const count = await apiServices.examReport.getCount(props.patient.visitSn)
    examReportCount.value = count
  } catch (error) {
    examReportCount.value = 0
  }
}

/**
 * 加载病理报告数量
 */
const loadPathologyReportCount = async () => {
  if (!props.patient?.visitSn) {
    pathologyReportCount.value = 0
    return
  }

  try {
    const count = await apiServices.pathologyReport.getCount(props.patient.visitSn)
    pathologyReportCount.value = count
  } catch (error) {
    pathologyReportCount.value = 0
  }
}

/**
 * 加载分子病理报告数量
 */
const loadMolecularPathologyCount = async () => {
  if (!props.patient?.visitSn) {
    molecularPathologyCount.value = 0
    return
  }

  try {
    const count = await apiServices.molecularPathology.getCount(props.patient.visitSn)
    molecularPathologyCount.value = count
  } catch (error) {
    molecularPathologyCount.value = 0
  }
}

/**
 * 加载检验报告数量
 */
const loadLabReportCount = async () => {
  if (!props.patient?.visitSn) {
    labReportCount.value = 0
    return
  }

  try {
    const count = await apiServices.labReport.getCount(props.patient.visitSn)
    labReportCount.value = count
  } catch (error) {
    labReportCount.value = 0
  }
}



// 组件挂载时发送当前选中项并加载数量
onMounted(() => {
  const menuName = getMenuItemName(selectedItem.value)
  emit('item-select', { key: selectedItem.value, name: menuName })

  // 加载数量数据
  if (props.patient?.visitSn) {
    loadDailyProgressCount()
    loadDiagnosisInfoCount()
    loadMedicalOrderCount()
    loadExamReportCount()
    loadPathologyReportCount()
    loadMolecularPathologyCount()
    loadLabReportCount()
  }
})

// 暴露方法给父组件调用
defineExpose({
  loadDailyProgressCount,
  loadDiagnosisInfoCount,
  loadMedicalOrderCount,
  loadExamReportCount,
  loadPathologyReportCount,
  loadMolecularPathologyCount,
  loadLabReportCount
})
</script>

<style scoped>
.medical-record-menu {
  width: 100%;
  background: transparent;
  padding: 0 0 0 8px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.menu-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.menu-section:last-child {
  margin-bottom: 0;
}

/* 移除旧的section-title样式，现在使用可展开的菜单项 */

.menu-item {
  width: 100%;
  min-height: 36px;
  border-radius: var(--border-radius-xs);
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: var(--transition-base);
  background: transparent;
  border: 1px solid transparent;
}

/* 可点击菜单项样式 */
.menu-item.clickable {
  padding: 0 var(--spacing-md);
  height: 36px;
  align-items: center;
  justify-content: flex-start;
}

.menu-item.clickable:hover {
  background-color: var(--color-primary-hover);
}

.menu-item.clickable.active {
  background-color: var(--color-primary-active);
  border: 1px solid var(--color-primary);
}

/* 可展开菜单项样式 */
.menu-item.expandable {
  cursor: pointer;
}

.menu-item.expandable .item-header:hover {
  background-color: var(--color-primary-hover);
}

.item-header {
  width: 100%;
  height: var(--height-md2);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 0 0 0 12px;
  border-radius: var(--border-radius-xs);
}

.item-text {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-md);
  color: var(--color-text-primary);
  line-height: var(--spacing-xl);
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.expand-icon {
  font-size: var(--font-size-sm);
  color: var(--color-primary);
  transition: var(--transition-base);
  width: var(--spacing-md);
  height: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  transform-origin: center;
}

.expand-icon::before {
  content: ">";
  transition: transform 0.2s ease;
}

.expand-icon.rotated::before {
  transform: rotate(90deg);
}

.sub-items {
  padding-left: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
}

.sub-item {
  width: 100%;
  height: var(--height-md2);
  border-radius: var(--border-radius-xs);
  display: flex;
  align-items: center;
  padding: 0 0 0 var(--spacing-3xl);
  cursor: pointer;
  transition: var(--transition-base);
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-md);
  color: var(--color-text-primary);
  line-height: var(--spacing-xl);
  text-align: left;
  font-style: normal;
  text-transform: none;
  background: transparent;
  border: 1px solid transparent;
}

.sub-item:hover:not(.active) {
  background: var(--color-primary-hover);
  border: 1px solid var(--color-primary-active);
}

.sub-item.active {
  background: var(--color-bg-primary);
  border-left: 2px solid var(--color-primary);
  border-radius: var(--border-radius-xs);
  color: var(--color-primary);
}

/* 三级菜单样式（病程记录的子项） */
.sub-menu-item {
  width: 188px;
  min-height: var(--height-md2);
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: var(--transition-base);
  background: transparent;
  border: 1px solid transparent;
  border-radius: var(--border-radius-xs);
}

.sub-item-header {
  width: 100%;
  height: var(--height-md2);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 0 var(--spacing-md);
  border-radius: var(--border-radius-xs);
}

.sub-item-header:hover {
  background-color: var(--color-primary-hover);
}

.sub-item-text {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-md);
  color: var(--color-text-primary);
  line-height: var(--spacing-xl);
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.sub-sub-items {
  padding-left: var(--spacing-3xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
}

.sub-sub-item {
  width: 172px;
  height: var(--height-md2);
  border-radius: var(--border-radius-xs);
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-sm);
  cursor: pointer;
  transition: var(--transition-base);
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-md);
  color: var(--color-text-primary);
  line-height: var(--spacing-xl);
  text-align: left;
  font-style: normal;
  text-transform: none;
  background: transparent;
  border: 1px solid transparent;
}

.sub-sub-item:hover:not(.active) {
  background: var(--color-primary-hover);
  border: 1px solid var(--color-primary-active);
}

.sub-sub-item.active {
  background: var(--color-bg-primary);
  border-left: 2px solid var(--color-primary);

  border-radius: var(--border-radius-xs);
  color: var(--color-primary);
}
</style>
