<template>
  <div class="patient-detail-panel">
    <!-- 患者头像和基本信息区域 -->
    <div class="patient-header-section">
      <!-- 患者头像 -->
      <div class="patient-avatar-section">
        <div class="patient-avatar">
          <PatientAvatar :patient="patient" :size="80" />
        </div>
      </div>

      <!-- 患者姓名和基本信息 -->
      <div class="patient-info-section">
        <!-- 患者姓名 -->
        <div class="patient-name-section">
          <h2 class="patient-name">{{ patient.patientName || '' }}</h2>
        </div>

        <!-- 基本信息行 -->
        <div class="basic-info-section">
          <span class="basic-info-text">
            {{ formatGenderAge(patient.gender, patient.patientAge) }}   {{ patient.ethnicity? "|" + patient.ethnicity : '' }}
          </span>
        </div>
      </div>
    </div>

    <!-- 详细信息列表 -->
    <div class="detail-info-section">
      <div class="info-row">
        <span class="info-label">病历号：</span>
        <span class="info-value">{{ patient.medicalRecordNo || '' }}</span>
      </div>

      <div class="info-row">
        <span class="info-label">床号：</span>
        <span class="info-value">{{ patient.bedNo }}</span>
      </div>

      <div class="info-row">
        <span class="info-label">入院时间：</span>
        <span class="info-value">{{ patient.admissionTime }}</span>
      </div>

      <div class="info-row">
        <span class="info-label">出院时间：</span>
        <span class="info-value">{{ patient.dischargeTime }}</span>
      </div>

      <div class="info-row">
        <span class="info-label">住院次数：</span>
        <span class="info-value">{{ patient.hospitalizationCount || '' }}</span>
      </div>

      <div class="info-row">
        <span class="info-label">住院天数：</span>
        <span class="info-value">{{ patient.hospitalizationDays || '' }}</span>
      </div>

      <div class="info-row">
        <span class="info-label">入院诊断：</span>
        <span class="info-value">{{ patient.admissionDiagnosis || '' }}</span>
      </div>

      <div class="info-row">
        <span class="info-label">管床医生：</span>
        <span class="info-value">{{ patient.attendingPhysician || '' }}</span>
      </div>
      <div class="remark">
        <span class="info-value">{{ patient.remark || '' }}</span>
      </div>
    </div>

    <!-- 底部医生插画 -->
    <div class="doctor-illustration">
      <img src="@/assets/images/doctor.png" alt="医生" class="doctor-image" />
    </div>
  </div>
</template>

<script setup>
import { formatGenderAge } from '@/utils/patientUtils.js'
import PatientAvatar from '@/components/common/PatientAvatar.vue'

// 定义组件props
defineProps({
  patient: {
    type: Object,
    required: true,
    default: () => ({})
  }
})




</script>

<style scoped>
/* 左侧患者信息详情面板 */
.patient-detail-panel {
  position: relative;
  width: 260px;
  height: 100%; /* 使用100%高度，与容器保持一致 */
  background: white;
  border-radius: 12px 0 0 12px;
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 20px 0px 20px;
}

/* 顶部背景图片 */
.patient-detail-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background-image: url('@/assets/images/detail-backgroud.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 0;
  border-radius: 12px 0 0 0;
}

/* 患者头像和基本信息区域 */
.patient-header-section {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: flex-start;
  gap: 25px;
  margin-bottom: 15px;
  width: 100%;
}

/* 患者头像区域 */
.patient-avatar-section {
  flex-shrink: 0;
}

/* 移除头像白色边框，让头像直接显示 */

/* 患者信息区域（姓名和基本信息） */
.patient-info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 80px; /* 与头像高度保持一致 */
}

/* 患者姓名区域 */
.patient-name-section {
  margin-bottom: 8px;
}

.patient-name {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 20px;
  color: #172B4D;
  line-height: 24px;
  margin: 0;
}

/* 基本信息行 */
.basic-info-section {
  /* 移除margin-bottom，因为现在在flex容器中 */
}

.basic-info-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #172B4D;
  line-height: 20px;
}

/* 详细信息列表 */
.detail-info-section {
  position: relative;
  z-index: 1;
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start; /* 更改为 flex-start 以处理多行文本 */
  padding: 8px 0; /* 稍微增加垂直间距 */
}
.remark{

  align-items: center;
  padding: 8px 0;
}

.info-label {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #6B778C;
  line-height: 20px;
  width: 75px; /* 设置固定宽度以对齐 */
  flex-shrink: 0; /* 防止在 flex 布局中被压缩 */
  text-align-last: end;
}

.info-value {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #172B4D;
  line-height: 20px;
  text-align: right;
  flex-grow: 1; /* 占据剩余空间 */
  word-break: break-all;
}

/* 底部医生插画 */
.doctor-illustration {
  position: relative;
  z-index: 1;
  margin-top: auto;
  display: flex;
  justify-content: left;
  align-items: flex-end;
  height: 200px;
  width: 100%;
}

.doctor-image {
  max-width: 110%;
  max-height: 110%;
  object-fit: contain;
  opacity: 0.8;
  margin-left: -5px;
}
</style>
