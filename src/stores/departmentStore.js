/**
 * 科室数据状态管理 - 优化版本
 * 专注核心功能：科室数据管理、选择状态、简单缓存
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { api } from '@/api'
import { getDataSource, getDataSourceName } from '@/utils/urlParams'

export const useDepartmentStore = defineStore('department', () => {
  // ========== 核心状态 ==========

  // 科室数据列表
  const departments = ref([])

  // 当前选中的科室ID
  const selectedDepartmentId = ref(null)

  // 加载状态
  const loading = ref(false)

  // 错误信息
  const error = ref(null)

  // 数据是否已初始化
  const initialized = ref(false)
  
  // ========== 计算属性 ==========

  // 当前选中的科室对象
  const selectedDepartment = computed(() => {
    if (!selectedDepartmentId.value) return null
    return departments.value.find(dept => dept.id === selectedDepartmentId.value) || null
  })

  // 科室总数
  const departmentCount = computed(() => departments.value.length)

  // 总患者数
  const totalPatientCount = computed(() => {
    return departments.value.reduce((total, dept) => total + (dept.patientCount || 0), 0)
  })

  // 是否有数据
  const hasData = computed(() => departments.value.length > 0)
  
  // ========== 核心方法 ==========

  // 防重复调用的Promise缓存
  let fetchPromise = null

  /**
   * 获取科室数据
   * @param {boolean} force - 是否强制刷新数据
   */
  const fetchDepartments = async (force = false) => {
    // 如果已经有数据且不是强制刷新，则直接返回
    if (initialized.value && hasData.value && !force) {
      return departments.value
    }

    // 如果正在请求中，返回同一个Promise避免重复请求
    if (fetchPromise && !force) {
      return await fetchPromise
    }

    // 创建新的请求Promise
    fetchPromise = (async () => {
      try {
        loading.value = true
        error.value = null

        // 获取数据源参数并调用API服务
        const dataSource = getDataSource()
        const departmentData = await api.getDepartmentStats(dataSource)

        // 更新数据
        departments.value = departmentData
        initialized.value = true

        // 如果没有选中的科室，默认选中第一个
        if (!selectedDepartmentId.value && departmentData.length > 0) {
          selectedDepartmentId.value = departmentData[0].id
        }

        return departmentData

      } catch (err) {
        error.value = err.message || '获取科室数据失败'

        // 使用Element Plus消息提示错误
        ElMessage.error('科室数据加载失败，请检查网络连接')

        // 清空数据，不使用假数据
        departments.value = []

        // 重新抛出错误，让调用方可以处理
        throw err

      } finally {
        loading.value = false
        // 清除Promise缓存，允许下次重新请求
        fetchPromise = null
      }
    })()

    return await fetchPromise
  }

  /**
   * 选择科室
   * @param {number|string} departmentId - 科室ID
   */
  const selectDepartment = (departmentId) => {
    const dept = departments.value.find(d => d.id === departmentId)
    if (dept) {
      selectedDepartmentId.value = departmentId
      return dept
    } else {
      ElMessage.warning('选择的科室不存在')
      return null
    }
  }

  /**
   * 清除选择状态
   */
  const clearSelection = () => {
    selectedDepartmentId.value = null
  }

  /**
   * 重置状态
   */
  const reset = () => {
    departments.value = []
    selectedDepartmentId.value = null
    loading.value = false
    error.value = null
    initialized.value = false
  }

  /**
   * 刷新数据
   */
  const refresh = async () => {
    try {
      const result = await fetchDepartments(true)
      ElMessage.success('科室数据刷新成功')
      return result
    } catch (error) {
      ElMessage.error('科室数据刷新失败')
      throw error
    }
  }

  /**
   * 根据ID获取科室信息
   * @param {number|string} departmentId - 科室ID
   */
  const getDepartmentById = (departmentId) => {
    return departments.value.find(dept => dept.id === departmentId) || null
  }



  // ========== 返回接口 ==========

  return {
    // 核心状态
    departments,
    selectedDepartmentId,
    selectedDepartment,
    loading,
    error,
    initialized,

    // 计算属性
    departmentCount,
    totalPatientCount,
    hasData,

    // 核心方法
    fetchDepartments,
    selectDepartment,
    clearSelection,
    reset,
    refresh,
    getDepartmentById
  }
})
