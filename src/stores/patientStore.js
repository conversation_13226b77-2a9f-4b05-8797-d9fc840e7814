/**
 * 患者状态管理Store - 最小化实现
 * 专注核心功能：数据获取、缓存管理、状态管理
 */

import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { api } from '@/api'
import { getDataSource } from '@/utils/urlParams'

export const usePatientStore = defineStore('patient', () => {
  // ========== 核心状态 ==========

  // 患者数据列表
  const patients = ref([])

  // 加载状态
  const loading = ref(false)

  // 错误信息
  const error = ref(null)

  // ========== 智能缓存配置 ==========

  const CACHE_TTL = 5 * 60 * 1000 // 5分钟缓存过期时间
  const MAX_CACHE_SIZE = 10 // 最多缓存10个科室数据

  // 缓存结构：{ data, timestamp, departmentCode }
  const cache = ref(new Map())

  // 防重复调用的Promise缓存
  const fetchPromiseCache = new Map()

  // ========== 计算属性 ==========

  // 是否有数据
  const hasData = computed(() => patients.value.length > 0)

  // 患者总数
  const patientCount = computed(() => patients.value.length)

  // ========== 缓存管理方法 ==========

  /**
   * 检查缓存是否有效
   * @param {string} cacheKey - 缓存键
   * @returns {boolean} 缓存是否有效
   */
  const isCacheValid = (cacheKey) => {
    if (!cache.value.has(cacheKey)) return false

    const cacheItem = cache.value.get(cacheKey)
    const now = Date.now()
    return (now - cacheItem.timestamp) < CACHE_TTL
  }

  /**
   * 清理过期缓存
   */
  const cleanExpiredCache = () => {
    const now = Date.now()
    for (const [key, item] of cache.value.entries()) {
      if ((now - item.timestamp) >= CACHE_TTL) {
        cache.value.delete(key)
      }
    }
  }

  /**
   * 限制缓存大小（LRU策略）
   */
  const limitCacheSize = () => {
    if (cache.value.size <= MAX_CACHE_SIZE) return

    // 找到最旧的缓存项并删除
    let oldestKey = null
    let oldestTime = Date.now()

    for (const [key, item] of cache.value.entries()) {
      if (item.timestamp < oldestTime) {
        oldestTime = item.timestamp
        oldestKey = key
      }
    }

    if (oldestKey) {
      cache.value.delete(oldestKey)
    }
  }

  // ========== 核心方法 ==========

  /**
   * 根据科室代码获取患者列表
   * @param {string|number} departmentCode - 科室代码
   * @param {boolean} force - 是否强制刷新数据
   * @param {string} source - 数据源参数 (可选，默认从URL获取)
   */
  const fetchPatientsByDepartment = async (departmentCode, force = false, source = null) => {
    if (!departmentCode) {
      console.warn('科室代码不能为空')
      return []
    }

    const cacheKey = String(departmentCode)

    // 清理过期缓存
    cleanExpiredCache()

    // 检查有效缓存
    if (!force && isCacheValid(cacheKey)) {
      const cachedData = cache.value.get(cacheKey).data
      patients.value = cachedData
      return cachedData
    }

    // 防重复请求
    if (fetchPromiseCache.has(cacheKey) && !force) {
      return await fetchPromiseCache.get(cacheKey)
    }

    // 创建请求Promise
    const fetchPromise = (async () => {
      try {
        loading.value = true
        error.value = null

        // 调用API获取患者数据（数据映射已在API层完成）
        // 如果没有传入source参数，从URL中获取
        const dataSource = source || getDataSource()
        const patientData = await api.getPatientsByDepartment(departmentCode, dataSource)

        // 更新状态
        patients.value = patientData

        // 缓存数据
        cache.value.set(cacheKey, {
          data: patientData,
          timestamp: Date.now(),
          departmentCode
        })

        // 限制缓存大小
        limitCacheSize()

        return patientData

      } catch (err) {
        console.error('获取患者数据失败:', err)
        error.value = err.message || '获取患者数据失败'
        patients.value = []
        throw err

      } finally {
        loading.value = false
        fetchPromiseCache.delete(cacheKey)
      }
    })()

    fetchPromiseCache.set(cacheKey, fetchPromise)
    return await fetchPromise
  }

  /**
   * 强制刷新数据
   * @param {string|number} departmentCode - 科室代码
   * @param {string} source - 数据源参数 (可选，默认从URL获取)
   */
  const refreshData = async (departmentCode, source = null) => {
    if (!departmentCode) {
      console.warn('刷新数据需要提供科室代码')
      return []
    }
    return await fetchPatientsByDepartment(departmentCode, true, source)
  }

  /**
   * 清空所有缓存
   */
  const clearCache = () => {
    cache.value.clear()
    fetchPromiseCache.clear()
  }

  /**
   * 获取指定患者信息
   * @param {string} patientId - 患者ID或病历号
   */
  const getPatientById = (patientId) => {
    return patients.value.find(patient =>
      patient.id === patientId ||
      patient.medicalRecordNo === patientId ||
      patient.visitSn === patientId
    )
  }

  /**
   * 重置Store状态
   */
  const reset = () => {
    patients.value = []
    loading.value = false
    error.value = null
    clearCache()
  }

  // ========== 返回接口 ==========
  return {
    // 核心状态
    patients,
    loading,
    error,

    // 计算属性
    hasData,
    patientCount,

    // 核心方法
    fetchPatientsByDepartment,
    refreshData,
    clearCache,
    getPatientById,
    reset
  }
})
