/**
 * 字典数据状态管理
 * 提供字典数据的缓存、加载和管理功能
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { apiServices } from '@/api'

export const useDictionaryStore = defineStore('dictionary', () => {
  // ========== 核心状态 ==========

  // 字典数据缓存 Map<dictType, { data, timestamp }>
  const dictionaries = ref(new Map())

  // 加载状态 Map<dictType, boolean>
  const loading = ref(new Map())

  // 错误信息 Map<dictType, string>
  const errors = ref(new Map())

  // ========== 缓存配置 ==========

  const CACHE_TTL = 30 * 60 * 1000 // 30分钟缓存过期时间
  const MAX_CACHE_SIZE = 20 // 最多缓存20种字典类型

  // 防重复调用的Promise缓存
  const fetchPromiseCache = new Map()

  // ========== 计算属性 ==========

  // 获取指定类型的字典数据
  const getDictionaryData = computed(() => {
    return (dictType) => {
      const cached = dictionaries.value.get(dictType)
      return cached ? cached.data : []
    }
  })

  // 获取指定类型的加载状态
  const isLoading = computed(() => {
    return (dictType) => loading.value.get(dictType) || false
  })

  // 获取指定类型的错误信息
  const getError = computed(() => {
    return (dictType) => errors.value.get(dictType) || null
  })

  // ========== 缓存管理方法 ==========

  /**
   * 检查缓存是否有效
   */
  const isCacheValid = (dictType) => {
    const cached = dictionaries.value.get(dictType)
    if (!cached) return false
    
    const now = Date.now()
    return (now - cached.timestamp) < CACHE_TTL
  }

  /**
   * 限制缓存大小
   */
  const limitCacheSize = () => {
    if (dictionaries.value.size <= MAX_CACHE_SIZE) return

    // 按时间戳排序，删除最旧的缓存
    const entries = Array.from(dictionaries.value.entries())
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp)
    
    const toDelete = entries.slice(0, entries.length - MAX_CACHE_SIZE)
    toDelete.forEach(([dictType]) => {
      dictionaries.value.delete(dictType)
      console.log(`删除过期字典缓存: ${dictType}`)
    })
  }

  /**
   * 清除指定类型的缓存
   */
  const clearCache = (dictType) => {
    if (dictType) {
      dictionaries.value.delete(dictType)
      errors.value.delete(dictType)
      console.log(`清除字典缓存: ${dictType}`)
    } else {
      dictionaries.value.clear()
      errors.value.clear()
      fetchPromiseCache.clear()
      console.log('清除所有字典缓存')
    }
  }

  // ========== 数据获取方法 ==========

  /**
   * 获取字典数据（通用方法）
   * @param {string} dictType - 字典类型
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Array>} 字典数据
   */
  const getDictionary = async (dictType, forceRefresh = false) => {
    if (!dictType) {
      throw new Error('dictType参数不能为空')
    }

    const cacheKey = dictType

    // 检查缓存
    if (!forceRefresh && isCacheValid(cacheKey)) {
      console.log(`使用缓存的字典数据: ${dictType}`)
      return getDictionaryData.value(dictType)
    }

    // 防重复调用
    if (fetchPromiseCache.has(cacheKey)) {
      console.log(`字典数据 ${dictType} 请求进行中，等待现有请求完成...`)
      return await fetchPromiseCache.get(cacheKey)
    }

    // 创建请求Promise
    const fetchPromise = (async () => {
      try {
        loading.value.set(dictType, true)
        errors.value.delete(dictType)

        console.log(`正在加载字典数据: ${dictType}`)

        // 调用API获取字典数据
        const data = await apiServices.dictionary.getByType(dictType)

        // 更新缓存
        dictionaries.value.set(dictType, {
          data,
          timestamp: Date.now()
        })

        // 限制缓存大小
        limitCacheSize()

        console.log(`字典数据 ${dictType} 加载成功:`, data)
        return data

      } catch (err) {
        console.error(`获取字典数据 ${dictType} 失败:`, err)
        const errorMessage = err.message || '获取字典数据失败'
        errors.value.set(dictType, errorMessage)
        
        // 显示错误提示
        ElMessage.error(`${dictType} 数据加载失败: ${errorMessage}`)
        
        throw err

      } finally {
        loading.value.set(dictType, false)
        fetchPromiseCache.delete(cacheKey)
      }
    })()

    fetchPromiseCache.set(cacheKey, fetchPromise)
    return await fetchPromise
  }

  /**
   * 获取邀请科室字典
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Array>} 邀请科室列表
   */
  const getInviteDepartments = async (forceRefresh = false) => {
    return await getDictionary('invite_department', forceRefresh)
  }

  /**
   * 获取诊断名称字典
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Array>} 诊断名称列表
   */
  const getDiagnosisNames = async (forceRefresh = false) => {
    return await getDictionary('diagnostic_name', forceRefresh)
  }

  /**
   * 获取诊断类型字典
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Array>} 诊断类型列表
   */
  const getDiagnosisTypes = async (forceRefresh = false) => {
    return await getDictionary('diag_type', forceRefresh)
  }

  /**
   * 获取是否主要诊断字典
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Array>} 是否主要诊断列表
   */
  const getYesOrNo = async (forceRefresh = false) => {
    return await getDictionary('yes_or_no', forceRefresh)
  }

  // ========== 医嘱相关字典方法 ==========

  /**
   * 获取医嘱类型字典
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Array>} 医嘱类型列表
   */
  const getMedicalOrderTypes = async (forceRefresh = false) => {
    return await getDictionary('order_type', forceRefresh)
  }

  /**
   * 获取医嘱类别字典
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Array>} 医嘱类别列表
   */
  const getMedicalOrderCategories = async (forceRefresh = false) => {
    return await getDictionary('order_class_name', forceRefresh)
  }

  /**
   * 获取医嘱名称字典
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Array>} 医嘱名称列表
   */
  const getMedicalOrderNames = async (forceRefresh = false) => {
    return await getDictionary('medical_order_name', forceRefresh)
  }

  /**
   * 根据医嘱类型获取医嘱名称字典
   * @param {string} orderTypeName - 医嘱类型名称，直接作为字典类型使用
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Array>} 对应类型的医嘱名称列表
   */
  const getMedicalOrderNamesByType = async (orderTypeName, forceRefresh = false) => {
    try {
      // 如果没有指定类型，返回默认的医嘱名称字典
      if (!orderTypeName) {
        return await getDictionary('medical_order_name', forceRefresh)
      }

      // 直接以医嘱类型名称作为字典类型请求数据
      // 例如：orderTypeName = "检查检验类医嘱"，就请求字典类型为"检查检验类医嘱"的数据
      console.log(`正在加载医嘱类型 "${orderTypeName}" 对应的医嘱名称字典`)
      const orderNames = await getDictionary(orderTypeName, forceRefresh)

      console.log(`医嘱类型 "${orderTypeName}" 对应的医嘱名称加载成功:`, orderNames)
      return orderNames

    } catch (error) {
      console.error(`获取医嘱类型 "${orderTypeName}" 对应的医嘱名称失败:`, error)
      // 出错时返回默认的医嘱名称字典作为备选
      try {
        return await getDictionary('medical_order_name', forceRefresh)
      } catch (fallbackError) {
        console.error('获取默认医嘱名称字典也失败:', fallbackError)
        return []
      }
    }
  }

  /**
   * 获取药品规格字典
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Array>} 药品规格列表
   */
  const getDrugSpecifications = async (forceRefresh = false) => {
    return await getDictionary('drug_specification', forceRefresh)
  }

  /**
   * 获取药品频次字典
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Array>} 药品频次列表
   */
  const getDrugFrequencies = async (forceRefresh = false) => {
    return await getDictionary('frequency_code', forceRefresh)
  }

  /**
   * 获取药品用法字典
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Array>} 药品用法列表
   */
  const getDrugUsageMethods = async (forceRefresh = false) => {
    return await getDictionary('administration_route', forceRefresh)
  }

  /**
   * 获取药品剂量单位字典
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Array>} 药品剂量单位列表
   */
  const getDrugDoseUnits = async (forceRefresh = false) => {
    return await getDictionary('drug_dose_unit', forceRefresh)
  }

  /**
   * 获取药品用量字典
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Array>} 药品用量列表
   */
  const getDrugDosages = async (forceRefresh = false) => {
    return await getDictionary('drug_dosage', forceRefresh)
  }

  /**
   * 刷新指定类型的字典数据
   * @param {string} dictType - 字典类型
   * @returns {Promise<Array>} 字典数据
   */
  const refresh = async (dictType) => {
    return await getDictionary(dictType, true)
  }

  /**
   * 批量预加载字典数据
   * @param {Array<string>} dictTypes - 字典类型数组
   */
  const preloadDictionaries = async (dictTypes = []) => {
    const promises = dictTypes.map(dictType => 
      getDictionary(dictType).catch(err => {
        console.warn(`预加载字典 ${dictType} 失败:`, err)
        return []
      })
    )
    
    await Promise.all(promises)
    console.log('字典数据预加载完成')
  }

  // ========== 返回状态和方法 ==========

  return {
    // 状态
    dictionaries: computed(() => dictionaries.value),
    loading: computed(() => loading.value),
    errors: computed(() => errors.value),

    // 计算属性
    getDictionaryData,
    isLoading,
    getError,

    // 方法
    getDictionary,
    getInviteDepartments,
    getDiagnosisNames,
    getDiagnosisTypes,
    getYesOrNo,
    getMedicalOrderTypes,
    getMedicalOrderCategories,
    getMedicalOrderNames,
    getMedicalOrderNamesByType,
    getDrugSpecifications,
    getDrugFrequencies,
    getDrugUsageMethods,
    getDrugDoseUnits,
    getDrugDosages,
    refresh,
    clearCache,
    preloadDictionaries,
    isCacheValid
  }
})
