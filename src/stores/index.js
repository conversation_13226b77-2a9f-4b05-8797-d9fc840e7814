/**
 * Stores 统一导出
 * 提供所有状态管理store的统一入口
 */

// 导入所有store
export { useDepartmentStore } from './departmentStore.js'
export { usePatientStore } from './patientStore.js'
export { useDictionaryStore } from './dictionaryStore.js'

// 未来可以添加更多store
// export { useUserStore } from './userStore.js'
// export { useAppStore } from './appStore.js'
// export { useSystemStore } from './systemStore.js'

/**
 * Store使用指南：
 * 
 * 1. 在组件中使用：
 *    import { useDepartmentStore } from '@/stores'
 *    const departmentStore = useDepartmentStore()
 * 
 * 2. 访问状态：
 *    departmentStore.departments
 *    departmentStore.selectedDepartment
 *    departmentStore.loading
 * 
 * 3. 调用方法：
 *    await departmentStore.fetchDepartments()
 *    departmentStore.selectDepartment(departmentId)
 * 
 * 4. 响应式使用：
 *    const { departments, loading } = storeToRefs(departmentStore)
 */
