<template>
  <DefaultLayout>
    <!-- 主要内容区域 -->
    <div class="home-content">
      <!-- 患者卡片列表 -->
      <div class="patient-section" v-if="selectedDepartment">
        <PatientCardList
          @patient-click="handlePatientClick"
          @view-detail="handlePatientDetail"
          @refresh="handlePatientRefresh"
        />
      </div>

    </div>
  </DefaultLayout>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import PatientCardList from '@/components/business/PatientCardList.vue'
import { useDepartmentStore, usePatientStore } from '@/stores'
import { getAllUrlParams, getDataSource } from '@/utils/urlParams'

// 路由相关
const router = useRouter()
const route = useRoute()

// 使用Pinia状态管理
const departmentStore = useDepartmentStore()
const patientStore = usePatientStore()

// 从store中获取响应式状态
const {
  selectedDepartment,
  selectedDepartmentId
} = storeToRefs(departmentStore)

// 从store中获取方法
const { fetchDepartments } = departmentStore

// 患者相关事件处理
const handlePatientClick = (patient) => {
  console.log('患者卡片点击:', patient)
  console.log('患者visitSn字段:', patient.visitSn, '类型:', typeof patient.visitSn)
  console.log('患者完整数据:', JSON.stringify(patient, null, 2))

  // 检查是否有visitSn
  if (!patient.visitSn) {
    console.error('visitSn检查失败，患者数据:', patient)
    ElMessage.error('患者数据不完整，缺少visitSn信息')
    return
  }

  // 跳转到患者详情页面，保持当前URL参数
  const currentParams = getAllUrlParams()
  router.push({
    name: 'PatientDetail',
    params: {
      visitSn: patient.visitSn
    },
    query: currentParams
  })
}

const handlePatientDetail = (patient) => {
  console.log('查看患者详情:', patient)

  // 检查是否有visitSn
  if (!patient.visitSn) {
    ElMessage.error('患者数据不完整，缺少visitSn信息')
    return
  }

  // 跳转到患者详情页面，保持当前URL参数
  const currentParams = getAllUrlParams()
  router.push({
    name: 'PatientDetail',
    params: {
      visitSn: patient.visitSn
    },
    query: currentParams
  })
}

const handlePatientRefresh = () => {
  console.log('患者数据刷新完成')
}

// 防重复调用标志
const isInitialLoading = ref(false)

// 监听科室选择变化，自动加载患者数据
watch(selectedDepartmentId, async (newDepartmentId, oldDepartmentId) => {
  // 避免初始化时的重复调用
  if (isInitialLoading.value) {
    console.log('跳过初始化期间的watch触发')
    return
  }

  if (newDepartmentId && newDepartmentId !== oldDepartmentId) {
    try {
      // 获取选中的科室对象
      const selectedDept = selectedDepartment.value
      const departmentCode = selectedDept?.code || selectedDept?.name || newDepartmentId

      console.log(`科室切换到: ${selectedDept?.name} (代码: ${departmentCode})，开始加载患者数据`)
      await patientStore.fetchPatientsByDepartment(departmentCode)
      console.log('患者数据加载完成')
    } catch (error) {
      console.error('患者数据加载失败:', error)
      ElMessage.error('患者数据加载失败')
    }
  }
}, { immediate: false })

// 组件挂载时获取数据
onMounted(async () => {
  try {
    isInitialLoading.value = true

    await fetchDepartments()
    console.log('首页数据加载完成')

    // 如果有选中的科室，立即加载患者数据
    if (selectedDepartmentId.value) {
      const selectedDept = selectedDepartment.value
      const departmentCode = selectedDept?.code || selectedDept?.name || selectedDepartmentId.value
      await patientStore.fetchPatientsByDepartment(departmentCode)
      console.log('初始患者数据加载完成')
    }
  } catch (err) {
    console.error('首页数据加载失败:', err)
  } finally {
    // 延迟重置标志，确保watch不会在初始化期间触发
    setTimeout(() => {
      isInitialLoading.value = false
    }, 100)
  }
})
</script>

<style scoped>
/* 首页内容样式 */
.home-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 患者区域 */
.patient-section {
  flex: 1;
  width: 100%;
  /* 移除overflow-y: auto，让父容器(.content-area)处理滚动 */
}


/* 响应式容器宽度控制 */
@media screen and (min-width: 1500px) {
  .home-content {
    max-width: 100%;
    margin: 0 auto;
  }
}

/* 大屏幕优化 */
@media screen and (min-width: 1920px) {
  .home-content {
    max-width: 1700px;
    margin: 0 auto;
  }
}
</style>
