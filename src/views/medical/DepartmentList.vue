<template>
  <DefaultLayout>
    <div class="department-list-page">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">科室管理</h1>
        <p class="page-subtitle">管理医院科室信息和患者分配</p>
      </div>

      <!-- 操作工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增科室
          </el-button>
          <el-button @click="handleRefresh" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索科室名称"
            style="width: 200px"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>

      <!-- 科室列表 -->
      <div class="department-grid">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="6" animated />
        </div>

        <!-- 错误状态 -->
        <div v-else-if="hasError" class="error-container">
          <el-empty description="数据加载失败">
            <el-button type="primary" @click="handleRefresh">重新加载</el-button>
          </el-empty>
        </div>

        <!-- 科室卡片列表 -->
        <div v-else class="department-cards">
          <div
            v-for="department in filteredDepartments"
            :key="department.id"
            class="department-card"
            :class="{ 'selected': selectedDepartmentId === department.id }"
            @click="handleSelectDepartment(department.id)"
          >
            <div class="card-header">
              <h3 class="department-name">{{ department.name }}</h3>
              <el-dropdown @command="handleCardAction">
                <el-button type="text" size="small">
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`edit-${department.id}`">编辑</el-dropdown-item>
                    <el-dropdown-item :command="`view-${department.id}`">查看详情</el-dropdown-item>
                    <el-dropdown-item :command="`delete-${department.id}`" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>

            <div class="card-content">
              <div class="stat-item">
                <span class="stat-label">患者数量</span>
                <span class="stat-value">{{ department.patientCount }}人</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">科室状态</span>
                <el-tag :type="department.status === 'active' ? 'success' : 'warning'">
                  {{ department.status === 'active' ? '正常' : '暂停' }}
                </el-tag>
              </div>
            </div>

            <div class="card-footer">
              <el-button size="small" @click.stop="handleViewPatients(department.id)">
                查看患者
              </el-button>
              <el-button size="small" type="primary" @click.stop="handleManage(department.id)">
                管理科室
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="!loading && !hasError">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[12, 24, 48]"
          :total="totalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </DefaultLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Search, MoreFilled } from '@element-plus/icons-vue'
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import { useDepartmentStore } from '@/stores'

// 路由
const router = useRouter()

// 状态管理
const departmentStore = useDepartmentStore()
const {
  departments,
  selectedDepartmentId,
  loading,
  departmentCount
} = storeToRefs(departmentStore)

// 本地状态
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(12)

// 计算属性
const filteredDepartments = computed(() => {
  let filtered = departments.value
  
  // 搜索过滤
  if (searchKeyword.value) {
    filtered = filtered.filter(dept => 
      dept.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }
  
  return filtered
})

const totalCount = computed(() => filteredDepartments.value.length)

// 方法
const handleAdd = () => {
  ElMessage.info('新增科室功能开发中...')
}

const handleRefresh = async () => {
  try {
    await departmentStore.refresh()
    // refresh方法内部已经包含了成功/失败的消息提示
  } catch (error) {
    // 错误消息已在Store中处理
    console.error('刷新失败:', error)
  }
}

const handleSearch = () => {
  currentPage.value = 1 // 搜索时重置到第一页
}

const handleSelectDepartment = (departmentId) => {
  departmentStore.selectDepartment(departmentId)
}

const handleCardAction = (command) => {
  const [action, id] = command.split('-')
  const departmentId = parseInt(id)
  
  switch (action) {
    case 'edit':
      ElMessage.info(`编辑科室 ${departmentId}`)
      break
    case 'view':
      ElMessage.info(`查看科室详情 ${departmentId}`)
      break
    case 'delete':
      handleDelete(departmentId)
      break
  }
}

const handleDelete = async (departmentId) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个科室吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.info('删除功能开发中...')
  } catch {
    // 用户取消删除
  }
}

const handleViewPatients = (departmentId) => {
  router.push({
    name: 'PatientManagement',
    query: { departmentId }
  })
}

const handleManage = (departmentId) => {
  ElMessage.info(`管理科室 ${departmentId}`)
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}

// 生命周期
onMounted(async () => {
  try {
    await departmentStore.fetchDepartments()
  } catch (error) {
    console.error('科室数据加载失败:', error)
  }
})
</script>

<style scoped>
.department-list-page {
  padding: var(--spacing-xl);
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: var(--spacing-2xl);
}

.page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--color-bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
}

.toolbar-left {
  display: flex;
  gap: var(--spacing-md);
}

.department-grid {
  min-height: 400px;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.department-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.department-card {
  background: var(--color-bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-base);
  cursor: pointer;
  border: 2px solid transparent;
}

.department-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.department-card.selected {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.department-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin: 0;
}

.card-content {
  margin-bottom: var(--spacing-lg);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.stat-label {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.stat-value {
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
}

.card-footer {
  display: flex;
  gap: var(--spacing-sm);
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-xl);
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .department-list-page {
    padding: var(--spacing-lg);
  }
  
  .toolbar {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .toolbar-left {
    width: 100%;
    justify-content: center;
  }
  
  .toolbar-right {
    width: 100%;
  }
  
  .department-cards {
    grid-template-columns: 1fr;
  }
}
</style>
