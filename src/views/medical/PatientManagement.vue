<template>
  <DefaultLayout>
    <div class="patient-management-page">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">患者管理</h1>
        <p class="page-subtitle">管理患者信息和医疗记录</p>
      </div>

      <!-- 筛选工具栏 -->
      <div class="filter-toolbar">
        <div class="filter-left">
          <el-select
            v-model="selectedDepartmentFilter"
            placeholder="选择科室"
            style="width: 200px"
            clearable
            @change="handleDepartmentFilter"
          >
            <el-option
              v-for="dept in departments"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
            />
          </el-select>
          
          <el-select
            v-model="statusFilter"
            placeholder="患者状态"
            style="width: 150px"
            clearable
            @change="handleStatusFilter"
          >
            <el-option label="住院中" value="inpatient" />
            <el-option label="门诊" value="outpatient" />
            <el-option label="已出院" value="discharged" />
          </el-select>
        </div>
        
        <div class="filter-right">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索患者姓名或病历号"
            style="width: 250px"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <el-button type="primary" @click="handleAddPatient">
            <el-icon><Plus /></el-icon>
            新增患者
          </el-button>
        </div>
      </div>

      <!-- 患者列表表格 -->
      <div class="table-container">
        <el-table
          :data="paginatedPatients"
          :loading="loading"
          stripe
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="id" label="病历号" width="100" />
          
          <el-table-column prop="name" label="患者姓名" width="120">
            <template #default="{ row }">
              <div class="patient-name">
                <el-avatar :size="32" :src="row.avatar">
                  {{ row.name.charAt(0) }}
                </el-avatar>
                <span>{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="gender" label="性别" width="80">
            <template #default="{ row }">
              <el-tag :type="row.gender === '男' ? 'primary' : 'success'" size="small">
                {{ row.gender }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="age" label="年龄" width="80" />
          
          <el-table-column prop="department" label="所属科室" width="120" />
          
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag
                :type="getStatusType(row.status)"
                size="small"
              >
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="admissionDate" label="入院日期" width="120" />
          
          <el-table-column prop="doctor" label="主治医生" width="100" />
          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="handleViewDetail(row)">
                查看
              </el-button>
              <el-button size="small" type="primary" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-dropdown @command="(command) => handleMoreAction(command, row)">
                <el-button size="small">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="medical-record">病历记录</el-dropdown-item>
                    <el-dropdown-item command="transfer">转科</el-dropdown-item>
                    <el-dropdown-item command="discharge" divided>出院</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredPatients.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedPatients.length > 0">
        <div class="batch-info">
          已选择 {{ selectedPatients.length }} 个患者
        </div>
        <div class="batch-buttons">
          <el-button @click="handleBatchTransfer">批量转科</el-button>
          <el-button @click="handleBatchExport">导出数据</el-button>
          <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
        </div>
      </div>
    </div>
  </DefaultLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, ArrowDown } from '@element-plus/icons-vue'
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import { useDepartmentStore } from '@/stores'

// 路由
const route = useRoute()
const router = useRouter()

// 状态管理
const departmentStore = useDepartmentStore()
const { departments, loading } = storeToRefs(departmentStore)

// 本地状态
const searchKeyword = ref('')
const selectedDepartmentFilter = ref(null)
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const selectedPatients = ref([])

// 模拟患者数据
const mockPatients = ref([
  {
    id: 'P001',
    name: '张三',
    gender: '男',
    age: 45,
    department: '肿瘤内科',
    status: 'inpatient',
    admissionDate: '2025-01-01',
    doctor: '李医生',
    avatar: ''
  },
  {
    id: 'P002',
    name: '李四',
    gender: '女',
    age: 38,
    department: '肿瘤外科',
    status: 'outpatient',
    admissionDate: '2025-01-02',
    doctor: '王医生',
    avatar: ''
  },
  {
    id: 'P003',
    name: '王五',
    gender: '男',
    age: 52,
    department: '放疗科',
    status: 'discharged',
    admissionDate: '2024-12-20',
    doctor: '赵医生',
    avatar: ''
  }
])

// 计算属性
const filteredPatients = computed(() => {
  let filtered = mockPatients.value

  // 科室筛选
  if (selectedDepartmentFilter.value) {
    const dept = departments.value.find(d => d.id === selectedDepartmentFilter.value)
    if (dept) {
      filtered = filtered.filter(patient => patient.department === dept.name)
    }
  }

  // 状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter(patient => patient.status === statusFilter.value)
  }

  // 搜索筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(patient =>
      patient.name.toLowerCase().includes(keyword) ||
      patient.id.toLowerCase().includes(keyword)
    )
  }

  return filtered
})

const paginatedPatients = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredPatients.value.slice(start, end)
})

// 方法
const getStatusType = (status) => {
  const types = {
    inpatient: 'danger',
    outpatient: 'warning',
    discharged: 'success'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    inpatient: '住院中',
    outpatient: '门诊',
    discharged: '已出院'
  }
  return texts[status] || '未知'
}

const handleDepartmentFilter = () => {
  currentPage.value = 1
}

const handleStatusFilter = () => {
  currentPage.value = 1
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleAddPatient = () => {
  ElMessage.info('新增患者功能开发中...')
}

const handleSelectionChange = (selection) => {
  selectedPatients.value = selection
}

const handleViewDetail = (patient) => {
  ElMessage.info(`查看患者详情: ${patient.name}`)
}

const handleEdit = (patient) => {
  ElMessage.info(`编辑患者信息: ${patient.name}`)
}

const handleMoreAction = (command, patient) => {
  switch (command) {
    case 'medical-record':
      ElMessage.info(`查看病历记录: ${patient.name}`)
      break
    case 'transfer':
      ElMessage.info(`转科操作: ${patient.name}`)
      break
    case 'discharge':
      handleDischarge(patient)
      break
  }
}

const handleDischarge = async (patient) => {
  try {
    await ElMessageBox.confirm(
      `确定要为患者 ${patient.name} 办理出院手续吗？`,
      '确认出院',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.info('出院功能开发中...')
  } catch {
    // 用户取消
  }
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}

const handleBatchTransfer = () => {
  ElMessage.info(`批量转科 ${selectedPatients.value.length} 个患者`)
}

const handleBatchExport = () => {
  ElMessage.info(`导出 ${selectedPatients.value.length} 个患者数据`)
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedPatients.value.length} 个患者吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.info('批量删除功能开发中...')
  } catch {
    // 用户取消
  }
}

// 生命周期
onMounted(async () => {
  // 加载科室数据
  try {
    await departmentStore.fetchDepartments()
  } catch (error) {
    console.error('科室数据加载失败:', error)
  }

  // 如果从科室管理页面跳转过来，设置科室筛选
  if (route.query.departmentId) {
    selectedDepartmentFilter.value = parseInt(route.query.departmentId)
  }
})
</script>

<style scoped>
.patient-management-page {
  padding: var(--spacing-xl);
  max-width: 1600px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: var(--spacing-2xl);
}

.page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
}

.filter-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--color-bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
}

.filter-left {
  display: flex;
  gap: var(--spacing-md);
}

.filter-right {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.table-container {
  background: var(--color-bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  margin-bottom: var(--spacing-xl);
}

.patient-name {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-xl);
}

.batch-actions {
  position: fixed;
  bottom: var(--spacing-xl);
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-bg-primary);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  z-index: 1000;
}

.batch-info {
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
}

.batch-buttons {
  display: flex;
  gap: var(--spacing-sm);
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .patient-management-page {
    padding: var(--spacing-lg);
  }
  
  .filter-toolbar {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .filter-left,
  .filter-right {
    width: 100%;
    justify-content: center;
  }
  
  .batch-actions {
    flex-direction: column;
    gap: var(--spacing-md);
  }
}
</style>
