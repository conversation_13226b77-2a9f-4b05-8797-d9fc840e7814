<template>
  <div class="test-page">
    <div class="test-header">
      <h1>分子病理报告测试页面</h1>
      <div class="patient-selector">
        <label>选择测试患者：</label>
        <select v-model="selectedPatientId" @change="handlePatientChange">
          <option value="">请选择患者</option>
          <option v-for="patient in testPatients" :key="patient.visitSn" :value="patient.visitSn">
            {{ patient.patientName }} - {{ patient.visitSn }}
          </option>
        </select>
      </div>
    </div>

    <div v-if="selectedPatient" class="test-content">
      <MolecularPathologyReport 
        :patient="selectedPatient" 
        @count-updated="handleCountUpdated"
      />
    </div>

    <div v-else class="no-patient">
      <p>请选择一个测试患者来查看分子病理报告</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import MolecularPathologyReport from '@/components/business/MolecularPathologyReport.vue'

// 测试患者数据
const testPatients = ref([
  {
    visitSn: 'TEST001',
    patientName: '张三',
    patientId: 'P001',
    gender: '男',
    age: 45,
    bedNo: '101',
    departmentName: '肿瘤科'
  },
  {
    visitSn: 'TEST002', 
    patientName: '李四',
    patientId: 'P002',
    gender: '女',
    age: 38,
    bedNo: '102',
    departmentName: '肿瘤科'
  }
])

// 当前选中的患者
const selectedPatientId = ref('')
const selectedPatient = ref(null)

// 处理患者选择变化
const handlePatientChange = () => {
  if (selectedPatientId.value) {
    selectedPatient.value = testPatients.value.find(p => p.visitSn === selectedPatientId.value)
  } else {
    selectedPatient.value = null
  }
}

// 处理数量更新事件
const handleCountUpdated = () => {
  console.log('分子病理报告数量已更新')
}
</script>

<style scoped>
.test-page {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.test-header {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #EBECF0;
}

.test-header h1 {
  margin: 0 0 16px 0;
  font-size: 24px;
  color: #172B4D;
}

.patient-selector {
  display: flex;
  align-items: center;
  gap: 12px;
}

.patient-selector label {
  font-weight: 500;
  color: #172B4D;
}

.patient-selector select {
  padding: 8px 12px;
  border: 1px solid #EBECF0;
  border-radius: 4px;
  font-size: 14px;
  min-width: 200px;
}

.test-content {
  flex: 1;
  overflow: hidden;
}

.no-patient {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-patient p {
  font-size: 16px;
  color: #6B778C;
}
</style>
