<!--
  患者详情页面第三方集成测试
  用于测试患者详情页面的第三方系统集成功能
-->
<template>
  <div class="integration-test">
    <div class="test-header">
      <h2>患者详情页面第三方集成测试</h2>
      <p>测试在患者详情页面打开时自动初始化第三方SDK并发送患者数据</p>
    </div>

    <!-- 集成状态显示 -->
    <div class="status-section">
      <h3>集成状态</h3>
      <el-card>
        <div class="status-item">
          <span class="status-label">SDK初始化状态:</span>
          <el-tag :type="integrationStatus.isInitialized ? 'success' : 'warning'">
            {{ integrationStatus.isInitialized ? '已初始化' : '未初始化' }}
          </el-tag>
        </div>
        
        <div class="status-item">
          <span class="status-label">患者数据发送状态:</span>
          <el-tag :type="integrationStatus.patientDataSent ? 'success' : 'info'">
            {{ integrationStatus.patientDataSent ? '已发送' : '未发送' }}
          </el-tag>
        </div>
        
        <div v-if="integrationStatus.error" class="status-item">
          <span class="status-label">错误信息:</span>
          <el-tag type="danger">{{ integrationStatus.error }}</el-tag>
        </div>
      </el-card>
    </div>

    <!-- 测试患者数据 -->
    <div class="test-data-section">
      <h3>测试患者数据</h3>
      <el-card>
        <el-form :model="testPatient" label-width="120px">
          <el-form-item label="患者ID">
            <el-input v-model="testPatient.patientId" />
          </el-form-item>
          <el-form-item label="患者姓名">
            <el-input v-model="testPatient.patientName" />
          </el-form-item>
          <el-form-item label="性别">
            <el-select v-model="testPatient.gender">
              <el-option label="男" value="男" />
              <el-option label="女" value="女" />
            </el-select>
          </el-form-item>
          <el-form-item label="就诊流水号">
            <el-input v-model="testPatient.visitSn" />
          </el-form-item>
          <el-form-item label="就诊类型">
            <el-select v-model="testPatient.visitType">
              <el-option label="住院" value="住院" />
              <el-option label="门诊" value="门诊" />
            </el-select>
          </el-form-item>
        </el-form>
        
        <div class="test-actions">
          <el-button type="primary" @click="testInitialization" :loading="testing.init">
            测试初始化
          </el-button>
          <el-button type="success" @click="testSendPatientData" :loading="testing.send">
            测试发送患者数据
          </el-button>
          <el-button @click="resetTest">重置测试</el-button>
        </div>
      </el-card>
    </div>

    <!-- 测试日志 -->
    <div class="log-section">
      <h3>测试日志</h3>
      <el-card>
        <div class="log-container">
          <div 
            v-for="(log, index) in testLogs" 
            :key="index"
            class="log-item"
            :class="log.type"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <div class="log-actions">
          <el-button size="small" @click="clearLogs">清空日志</el-button>
        </div>
      </el-card>
    </div>

    <!-- 跳转到真实患者详情页面 -->
    <div class="navigation-section">
      <h3>导航测试</h3>
      <el-card>
        <p>使用测试数据跳转到真实的患者详情页面进行集成测试</p>
        <el-button type="primary" @click="goToPatientDetail" :disabled="!testPatient.visitSn">
          跳转到患者详情页面
        </el-button>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import thirdPartyIntegration from '@/services/thirdPartyIntegration'
import { PatientDataAdapter } from '@/utils/patientDataAdapter'
import { getCurrentConfig } from '@/config/thirdPartyConfig'
import { apiServices } from '@/api'

const router = useRouter()

// 集成状态
const integrationStatus = reactive({
  isInitialized: false,
  isInitializing: false,
  patientDataSent: false,
  error: null
})

// 测试状态
const testing = reactive({
  init: false,
  send: false
})

// 测试患者数据
const testPatient = reactive({
  patientId: '88888888',
  patientName: '测试患者',
  gender: '男',
  visitSn: '88888888',
  visitType: '住院',
  medicalRecordNo: 'MR001',
  inpatientNo: 'IP001',
  admissionDatetime: '2024-01-15 10:30:00'
})

// 测试日志
const testLogs = ref([])

// 添加日志
const addLog = (message, type = 'info') => {
  testLogs.value.push({
    time: new Date().toLocaleTimeString(),
    message,
    type
  })
}

// 清空日志
const clearLogs = () => {
  testLogs.value = []
}

// 测试初始化
const testInitialization = async () => {
  testing.init = true
  addLog('开始测试第三方系统初始化...', 'info')
  
  try {
    integrationStatus.error = null
    
    // 获取配置
    const config = getCurrentConfig('yingchunhua')
    addLog(`获取配置成功: ${JSON.stringify(config)}`, 'success')
    
    // 初始化
    await thirdPartyIntegration.initialize({
      ...config,
      deptId: '0001',
      doctorId: '0002'
    })
    
    integrationStatus.isInitialized = true
    addLog('第三方系统初始化成功', 'success')
    ElMessage.success('初始化成功')
    
  } catch (error) {
    integrationStatus.error = error.message
    addLog(`初始化失败: ${error.message}`, 'error')
    ElMessage.error('初始化失败: ' + error.message)
  } finally {
    testing.init = false
  }
}

// 获取患者历史数据
const fetchPatientHistoryData = async (visitSn) => {
  try {
    addLog(`开始获取患者历史数据: ${visitSn}`, 'info')

    // 使用统一的API服务获取患者历史数据
    const historyData = await apiServices.thirdPartyIntegration.getPatientHistory(visitSn)

    addLog('患者历史数据获取成功', 'success')
    return historyData

  } catch (error) {
    addLog(`获取患者历史数据失败: ${error.message}`, 'error')
    throw error
  }
}

// 测试发送患者数据
const testSendPatientData = async () => {
  if (!integrationStatus.isInitialized) {
    ElMessage.warning('请先初始化第三方系统')
    return
  }

  testing.send = true
  addLog('开始测试发送患者数据...', 'info')

  try {
    // 获取患者历史数据
    const historyData = await fetchPatientHistoryData(testPatient.visitSn)

    addLog(`获取到的历史数据: ${JSON.stringify(historyData, null, 2)}`, 'info')

    // 发送数据
    await thirdPartyIntegration.sendPatientData(historyData)

    integrationStatus.patientDataSent = true
    addLog('患者数据发送成功', 'success')
    ElMessage.success('数据发送成功')

  } catch (error) {
    integrationStatus.error = error.message
    addLog(`数据发送失败: ${error.message}`, 'error')
    ElMessage.error('发送失败: ' + error.message)
  } finally {
    testing.send = false
  }
}

// 重置测试
const resetTest = () => {
  integrationStatus.isInitialized = false
  integrationStatus.patientDataSent = false
  integrationStatus.error = null
  clearLogs()
  addLog('测试已重置', 'info')
}

// 跳转到患者详情页面
const goToPatientDetail = () => {
  router.push({
    name: 'PatientDetail',
    params: {
      visitSn: testPatient.visitSn
    }
  })
}

onMounted(() => {
  addLog('患者详情页面第三方集成测试页面已加载', 'info')
})
</script>

<style scoped>
.integration-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  margin-bottom: 30px;
  text-align: center;
}

.test-header h2 {
  color: #409EFF;
  margin-bottom: 10px;
}

.status-section,
.test-data-section,
.log-section,
.navigation-section {
  margin-bottom: 30px;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.status-label {
  margin-right: 10px;
  font-weight: bold;
  min-width: 150px;
}

.test-actions {
  margin-top: 20px;
  text-align: center;
}

.test-actions .el-button {
  margin: 0 10px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 10px;
  background-color: #FAFAFA;
}

.log-item {
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.log-item.success {
  color: #67C23A;
}

.log-item.error {
  color: #F56C6C;
}

.log-item.info {
  color: #409EFF;
}

.log-time {
  margin-right: 10px;
  color: #909399;
}

.log-actions {
  margin-top: 10px;
  text-align: right;
}

.navigation-section {
  border-top: 2px solid #E4E7ED;
  padding-top: 20px;
}
</style>
