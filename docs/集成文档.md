
![图标

中度可信度描述已自动生成](data:image/png;base64,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)                                                                            OTD\-0001

医院端产品界面集成

接

口

说

明

2025年7月

修订历史记录

日期

版本

说明

作者

# 整体说明

该文档主要描述了我方系统在医院内如何与第三方的界面进行集成，包括了具体对接的系统，对接的方式

## 适用角色

- 公司研发：掌握医院的对接方案，完成产品接口的开发与升级
- 公司交付经理：用于项目交付判断接口的完成情况
- 第三方厂商或者信息科：用于第三方完成接口开发

# 对接场景

## 对接系统场景

系统类型

对接场景

备注

HIS

在院患者选中一个患者

必须对接

HIS

门诊患者选中一个患者

必须对接

HIS

出院患者选中一个患者

必须对接

EMR

在院患者选中一个患者

必须对接

EMR

出院患者选中一个患者

必须对接

## 功能清单

产品类型

患者类型

功能模块

备注

迎春花

在院

过程管理

门诊

过程管理

在院

质控结果

门诊

质控结果

在院

质控日志

出院

质控日志

在院

费用管理

对接DRG系统

出院

费用管理

对接DRG系统

出院

质控小结

    此处需要明确上线的产品覆盖内容！！！

# B/S版本对接方案

## 前置依赖

建议浏览器版本: IE 11 、chorme 49及以上。如果不具备及时沟通！！

## 获取APPKEY

由我方现场项目经理提供！！！

## 对接流程图

![IMG_256](data:image/png;base64,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)

## 对接接口

### 加载患者

所有公开方法在window\.onload内进行 确保方法均已加载

### 加载JS

HTML页面引入方式 HTML页面javascript 引入

实例:

<script src=”http://172\.17\.1\.103:5001/client\_app\_iframe/index\.js?linkType=2”></script>

- 参数：linkType:  1 app  2 web  医院如果是网页版选择2，医院如果CS结构选择1
- http://172\.17\.1\.103:5001一定与现场的一致（由我方现场项目经理提供）

所有公开方法在window\.onload内进行 确保方法均已加载

#### 示例

window\.onload = function\(\) \{

	var params = \{ 

		"deptId": "0001",  

		"doctorId": "0002",  

		"appKey": "ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09",  

		"appSecretKey": "YYCloud1644286723584",  

		"qualityTarget": "2"  

	\} 

	//设置当前科室和医生 

	\_clientAppSDK\.changeParams\(params\) 

	var history = \{

	"patientId": "00931222",

	"visitSn": "00931222|4623477|1住院",

	"dataPacket": \[

		\{

			"tableCode": "b02\_1",

			"data: \[

				\{

					"patient\_id": "00931222",

					"visit\_sn": "00931222|4623477|1住院",

"visit\_type": "住院" ,

                            "hospital\_code":"25ab66fb45de3339c17a53", 

                   "hospital\_name": "山西省肿瘤医院", 

                   "visit\_card\_no": "ttt1234", 

                   "outpatient\_no": "", 

                   "visit\_times": "", 

                   "visit\_datetime": "", 

                   "medical\_record\_no": "", 

                   "inpatient\_no": "102310", 

                   "hospitalization\_times": "1", 

                   "admission\_datetime": "2022\-04\-16 07:11:07", 

                   "discharge\_datetime": "2022\-04\-16 07:11:07",

                   "visit\_doctor\_no": "001346", 

                   "visit\_doctor\_name": "张三", 

                   "name": "李四", 

                   "gender": "男",

                   "patient\_gender": "NULL", 

                   "date\_of\_birth": "1987\-05\-25", 

                   "occupation\_code": "",

                   "occupation\_name": "程序员", 

                   "nationality": "中国", 

                   "ethnicity": "汉族", 

                   "education": "小学毕业", 

                   "education\_code": "12",  

                   "marital\_status": "已婚",

                   "marital\_status\_code": "", 

                   "newbron\_mark": "否", 

                   "visit\_status": "是", 

                   "patient\_identity": "其他",

                   "blood\_type\_s": "NULL", 

                   "bolld\_type\_e": "NULL",

                   "height": "NULL", 

                   "weight": "NULL", 

                   "certificate\_type": "身份证", 

                   "certificate\_no": "xxxxxxxxxxxxxxxxxx", 

                   "idcard\_no": "xxxxxxxxxxxxxxxxxx", 

                   "health\_card\_type": "NULL", 

                   "health\_card\_no": "NULL", 

                   "insurance\_type": "云南省属地州职工医保", 

                   "insurance\_no": "HZ2501085623", 

                   "domicile\_province": "",

                   "domicile\_city": "", 

                   "domicile\_county": "", 

                   "domicile\_address": "", 

                   "home\_address": "", 

                   "phone\_no": "18877887778",

                   "phone\_no2": "", 

                   "email": "", 

                   "weixin": "", 

                   "contact\_person1": "",

                   "contact\_phone\_no1": "",

                   "contact\_person2": "", 

                   "contact\_phone\_no2": "",

                   "abo\_blood\_type": "", 

                   "rh\_blood\_type": "", 

                   "tsblbs": "NULL", 

                   "is\_hospital\_infected": "NULL", 

                   "extend\_data1": "NULL", 

                   "extend\_data2": "NULL", 

                   "record\_status": "1", 

                   "record\_datetime": "",

                   "record\_update\_datetime": "", 

                   "admission\_dept\_code": "034",

                   "admission\_dept\_name": "肿瘤内科",

                   "admission\_ward\_code": "123",

                   "admission\_ward\_name": "化疗病房",

                   "admission\_bed\_code": "", 

                   "admission\_bed\_name": "28床",

                   "current\_dept\_code": "034", 

                   "current\_dept\_name": "肿瘤内科", 

                   "current\_ward\_code": "123", 

                   "current\_ward\_name": "化疗病房", 

                   "current\_bed\_code": "", 

                   "current\_bed\_name": "28床", 

                   "admission\_medical\_team\_code": "sn1", 

                   "admission\_medical\_team\_name": "神经内诊疗一组", 

                   "chief\_physician\_id": "456", 

                   "chief\_physician": "王五", 

                   "attending\_physician\_id": "001346", 

                   "attending\_physician": "张文宏",

                   "responsible\_nurse\_id": "", 

                   "responsible\_nurse": "", 

                   "dmission\_type\_code": "w01",

                   <a id="_Hlk149241892"></a>"admission\_type\_name": "急诊"		

				\}

			\]

		\}

	\]

\}

	//请求历史数据，住院患者 

	\_clientAppSDK\.history\(history\) 

\}

##### 通过授权认证及设置科室

方法名称：\_clientAppSDK\.changeParams

方法示例：

\_clientAppSDK\.changeParams\(\{

	"deptId": "0001",  

	"doctorId": "0002",  

	"appKey": "ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09",  

	"appSecretKey": "YYCloud1644286723584",  

	"qualityTarget": "2"  

\}\);

- deptId\(必填\)：为当前医生登录的科室id，不能为空,和离线采集的科室id一致。
- doctorId\(必填\)：为当前医生的id，不能为空，和离线采集的科室id一致。
- appKey\(必填\)：为线下获取，不能为空
- appSecreKey\(必填\)：为线下获取，不能为空
- qualityTarget: 为下表的对应值,非必填

值

名称

1

临床

2

病理

##### <a id="_设置患者相关信息"></a>__设置患者相关信息__

方法名称：\_clientAppSDK\.history

方法示例：

- 住院患者（出院/在院）

\_clientAppSDK\.history\(\{

	patientId: "00931222",  

	visitSn: "00931222|4623477|1住院", 

	dataPacket: \[

		\{

			tableCode: "b02\_1",

			data: \[

				\{

					patient\_id: "00931222", //患者P（必填），wotu\_ods\.b02\_0中patient\_id\_old字段

					visit\_sn: "00931222|4623477|1住院", //患者就诊ID（必填），wotu\_std\.patient\_visit\_base\_info中visit\_sn字段

                       visit\_type: "住院", //就诊类型（必填）：门诊（急诊）、住院                    

hospital\_code: "25ab66f1a2a2421e9b45de3339c17a53", //医院编码（必填）：患者登记场景下必填，可以从聚合配置默认值

                    hospital\_name: "山西省肿瘤医院", //医院名称（必填），可以从聚合配置默认值 

                    visit\_card\_no: "ttt1234", //就诊卡号：患者在本医院就诊时使用的就诊卡号

                    outpatient\_no: "", //门诊号：当visit\_type为门诊时，不允许为空；当visit\_type为住院时，不允许有值

                    visit\_times: "", //就诊次数：该字段在门诊号相同的情况下不允许同时出现有值、为空两种情况

                    visit\_datetime: "", //就诊时间：当visit\_type为门诊时，不允许为空；当visit\_type为住院时，不允许有值；该字段在门诊号相同的情况，不允许出现重复

                    medical\_record\_no: "", //病案号（必填）:该字段在Visit\_type为门诊的情况下，不允许有值；该字段在Visit\_type为住院的情况下，原则上不允许为空；与病案首页关联一致，如果不可以与病案首页关联该值为空

                    inpatient\_no: "102310", //住院号（必填）

                    hospitalization\_times: "1", //住院次数：住院号相同的情况下不允许重复；允许为空值

                    admission\_datetime: "2022\-04\-16 07:11:07", //入院时间，格式为：yyyy\-MM\-dd HH:mm:ss",visit\_type为住院时必填；visit\_type为门诊时不能有值

                    discharge\_datetime: "2022\-04\-16 07:11:07", //出院时间，格式为：yyyy\-MM\-dd HH:mm:ss"

                    visit\_doctor\_no: "001346", //就诊医生代码（必填）：住院：主治医生工号、门诊：就诊医生工号

                    visit\_doctor\_name: "张三", //就诊医生姓名（必填）：住院：主治医生姓名、门诊：就诊医生姓名

                    name: "李四", //患者姓名（必填）

                    gender: "男", //性别（必填）：医院原始性别名称

                    patient\_gender: "NULL", //性别编码 

                    date\_of\_birth: "1987\-05\-25", //出生日期（必填）：出生日期：yyyy\-MM\-dd

                    occupation\_code: "", //职业类别代码（必填）：填写医院原始代码

                    occupation\_name: "程序员",//职业类别名称（必填）：取医院原始值:名称

                    nationality: "中国",//国籍

                    ethnicity: "汉族", //民族

                    education: "小学毕业", //文化程度

                    education\_code: "12", //文化程度代码

                    marital\_status: "已婚",//婚姻状况类别名称（必填）：取医院原始值:如未婚、已婚、丧偶、离婚等

                    marital\_status\_code: "",//婚姻状况类别代码：取医院原始值:代码

                    newbron\_mark: "否", //是否新生儿

                    visit\_status: "是",//是否在院：门诊患者此处为 否，患者在院此处为 是

                    patient\_identity: "其他", //患者身份

                    blood\_type\_s: "NULL",//血型首位代码

                    bolld\_type\_e: "NULL", //血型末位代码

                    height: "NULL", //身高

                    weight: "NULL",//体重

                    certificate\_type: "身份证", //证件类型（必填）：填写注册证件类型对应的中文名称，如居民身份证、护照等。

                    certificate\_no: "xxxxxxxxxxxxxxxxxx", //证件号码（必填）：患者证件号码

                    idcard\_no: "xxxxxxxxxxxxxxxxxx", //身份证号码（必填）

                    health\_card\_type: "NULL", //健康卡类型

                    health\_card\_no: "NULL", //健康卡号

                    insurance\_type: "云南省属地州职工医保", //医保类型：患者医保类型

                    insurance\_no: "HZ2501085623", //医保卡号

                    domicile\_province: "",//户籍地址\-省名称

                    domicile\_city: "",//户籍地址\-市名称

                    domicile\_county: "",//户籍地址\-县名称

                    domicile\_address: "",//户籍地址\-详细地址

                    home\_address: "",//现住址

                    phone\_no: "18877887778",//联系电话（必填）

                    phone\_no2: "",//联系电话2,不能与phone\_no相同

                    email: "",//邮箱

                    weixin: "", //微信

                    contact\_person1: "", //紧急联系人1

                    contact\_phone\_no1: "", //紧急联系人电话1

                    contact\_person2: "", //紧急联系人2

                    contact\_phone\_no2: "",//紧急联系人电话2

                    abo\_blood\_type: "", //ABO血型

                    rh\_blood\_type: "", //Rh血型

                    tsblbs: "NULL", //特殊病例标识

                    is\_hospital\_infected: "NULL", //是否院内感染

                    extend\_data1: "NULL", //扩展字段1

                    extend\_data2: "NULL", //扩展字段2

                    record\_status: "1", //记录状态（必填）：1正常0作废

                    record\_datetime: "", //记录创建时间（必填）：格式为：yyyy\-MM\-dd HH:mm:ss

                    record\_update\_datetime: "", //记录更新时间

                    admission\_dept\_code: "034",//入院科室代码（必填）

                    admission\_dept\_name: "肿瘤内科",//入院科室名称（必填）

                    admission\_ward\_code: "123",//入院病区代码（必填）

                    admission\_ward\_name: "化疗病房",//入院病区名称（必填）

                    admission\_bed\_code: "",//入院床位编码

                    admission\_bed\_name: "28床",//入院床位名称（必填）

                    current\_dept\_code: "034",//当前科室代码（必填）：患者当前所在科室代码

                    current\_dept\_name: "肿瘤内科", //当前病区名称（必填）：患者当前所在病区名称

                    current\_ward\_code: "123",//当前病区代码（必填）：患者当前所在病区代码

                    current\_ward\_name: "化疗病房",//当前病区名称（必填）：患者当前所在病区名称

                    current\_bed\_code: "", //当前床位代码：患者当前床位代码

                    current\_bed\_name: "28床",//当前床位名称（必填）：患者当前床位名称

                    admission\_medical\_team\_code: "sn1",//入院医疗组代码（必填）：入院医疗组代码

                    admission\_medical\_team\_name: "神经内科诊疗一组", //入院医疗组名称（必填）：入院医疗组名称

                    chief\_physician\_id: "456",//主任医师代码

                    chief\_physician: "王五",//主任医师名称

                    attending\_physician\_id: "001346",//主治医师代码

                    attending\_physician: "张文宏",//主治医师名称

                    responsible\_nurse\_id: "",//责任护士代码

                    responsible\_nurse: "",//责任护士名称

                    admission\_type\_code: "w01",//入院方式代码（必填）

                    admission\_type\_name: "急诊"//入院方式名称（必填）：急诊、门诊、其他医疗机构转入、其他				

\}

			\]

		\}

	\]

\}\)

- 门诊/体检患者

\_clientAppSDK\.history\(

\{

	"patientId": "00931222",

	"visitSn": "00931222|4623477|1门诊",

	"dataPacket": \[

		\{

			"tableCode": "b12\_1",

			"data": \[

				\{

					"patient\_id": "00931222", //患者id,必填

					"visit\_sn": "00931222|4623477|1门诊",//单次就诊唯一标识,必填

					"visit\_type": "门诊",//就诊类型，必填

					"hospital\_code": "25ab66fb45de3339c17a53", //医院编码，选填

					"hospital\_name": "山西省肿瘤医院", //医院名称，选填

					"visit\_card\_no": "",//就诊卡号,选填

					"outpatient\_no": "4623477", //门诊号,必填

					"visit\_times": "", //就诊次数，选填,1\. 该字段代表该患者在医院的门诊次数2\. 该字段在门诊号相同的情况下不允许重复3\. 该字段在门诊号相同的情况下应该是连续的正整数4\. 该字段允许为空值5\. 该字段在门诊号相同的情况下不允许同时出现有值、为空两种情况

					"visit\_datetime": "2020\-04\-21 09:45:34",//就诊时间

					"medical\_record\_no": "", //病案号，visit\_type为门诊时不允许有值

					"inpatient\_no": "", //住院号，visit\_type为门诊时不允许有值

					"hospitalization\_times": "",//住院次数

					"admission\_datetime": "",//入院时间

					"discharge\_datetime": "",//出院时间

					"visit\_doctor\_no": "001346",//就诊医生代码

					"visit\_doctor\_name": "张三",//就诊医生名称

					"name": "李四", //患者姓名

					"gender": "男", //性别

					"patient\_gender": "NULL",//性别编码，选填

					"date\_of\_birth": "1987\-05\-25",//出生日期，原则上不为空

					"occupation\_code": "", //职业类别代码

					"occupation\_name": "程序员", //职业类别名称,必填

					"nationality": "中国", //国籍，必填

					"ethnicity": "汉族", //民族，必填

					"education": "小学毕业", //文化程度，必填

					"education\_code": "12", //文化程度代码，选填

					"marital\_status": "已婚", //婚姻状况名称，必填

					"marital\_status\_code": "", //婚姻状况类别代码,选填

					"newbron\_mark": "否", //是否新生儿，必填

					"visit\_status": "否", //是否在院，必填，门诊患者此处为否

					"patient\_identity": "其他", //患者身份，必填

					"blood\_type\_s": "NULL", //血型首位代码,选填

					"bolld\_type\_e": "NULL",//血型末代码，选填

					"height": "NULL",//身高,选填

					"weight": "NULL", //体重，选填

					"certificate\_type": "身份证", //证件类型，必填

					"certificate\_no": "xxxxxxxxxxxxxxxxxx",//证件号码，必填

					"idcard\_no": "xxxxxxxxxxxxxxxxxx",//身份证号码，必填

					"health\_card\_type": "NULL",//健康卡类型,选填

					"health\_card\_no": "NULL",//健康卡号,选填

					"insurance\_type": "云南省属地州职工医保",//医保类型，必填，不允许为空

					"insurance\_no": "HZ2501085623",//医保卡号，必填，在医保类型有值的情况下，不允许为空

					"domicile\_province": "",//户籍地址\-省名称

					"domicile\_city": "昆明市",//户籍地址\-市名称,选填:该字段原则不允许为空																				   "domicile\_county": "", //户籍地址\-县名称,选填

					"domicile\_address": "", //户籍地址\-详细地址，选填

					"home\_address": "", //现住址

					"phone\_no": "18877887778", //联系电话，必填

					"phone\_no2": "",//联系电话2，选填

					"email": "", //邮箱，选填

					"weixin": "", //微信，选填

					"contact\_person1": "", //紧急联系人1，选填

					"contact\_phone\_no1": "",//紧急联系人电话1，选填

					"contact\_person2": "",//紧急联系人2，选填

					"contact\_phone\_no2": "",//紧急联系人电话2，选填

					"abo\_blood\_type": "A型",//ABO血型，原则不允许为空

					"rh\_blood\_type": "阴性",//Rh血型,原则不允许为空

					"tsblbs": "NULL", //特殊病例标识，选填

					"is\_hospital\_infected": "NULL",//是否院内感染，选填

					"extend\_data1": "NULL", //扩展字段1，原则上不使用

					"extend\_data2": "NULL",//拓展字段2，原则上不适用

					"record\_status": "1", //记录状态，必填：该字段不为空，0，1，记录状态为空时取1

					"record\_datetime": "2020\-04\-21 09:45:34", //记录创建时间，必填，医院业务系统产生该记录的时间，为空取就诊时间

					"record\_update\_datetime": "", //记录更新时间，选填

					"regis\_sn":"13423",//挂号流水号，必填，原则不可为空

					"regis\_datetime":"2020\-04\-21 09:45:34",//挂号时间，必填，原则不可为空

					"first\_visit\_mark":"是",//是否出诊，选填，原则上不为空

					"regis\_method\_code":"3",//挂号渠道代码,原则上不为空

					"regis\_method":"现场预约",//挂号渠道名称,原则上不为空

					"regis\_type\_code":"3",//挂号类型代码,选填，原则上不为空

					"regis\_type":"普通号",//挂号类型，原则上不为空

					"regis\_charge\_price":"5\.000",//挂号金额（元）,选填，原则上不为空，数值，四位小数点以内，大于等于0

					"regis\_paid\_price":"5\.000",//实付金额\(元\),选填，原则上不为空，数值，四位小数点以内，大于等于0

					"regis\_dept\_code":"15",//挂号科室代码,必填

					"regis\_dept\_name":"肿瘤科",//挂号科室名称,必填

					"technical\_title":"主治医师",//医师职称名称

					"job\_title":"科室主任"//医师职务名称

				\}

			\]

		\}

	\]

\}

\)

- patientId\(必填\):患者的id，需要保证与离线数据的患者id一致（一定要是患者的唯一标识）,
- visits\(必填\):就诊id，需要保证与离线数据的就诊id一致（一定要是患者就诊的唯一标识）,
- dataPacket\(必填\):填写后dataPacket里数据必须按以下配置传参
  - 
  	- table\_code\(dataPacket必填\):出院/在院传b02\_1,门诊/体检传b12\_1
  	- 内部patient\_id\(dataPacket必填\):需要保证与离线数据的患者id一致（一定要是患者的唯一标识）
  	- 内部visit\_sn\(dataPacket传了则必填\):需要保证与离线数据的就诊id一致（一定要是患者就诊的唯一标识）
  	- visit\_type\(必填\): 患者当前就诊的入院类型，取值为门诊、住院、体检。
  	- 如果visit\_sn无法与离线的保持一致，需要其它字段拼接实现一致，则dataPacket里面的这些需要拼接字段必填，同时现场项目经理在聚合服务\(qc\_single\_disease\.datapacket\_config\)里进行相应配置

### 信息反填（未实现）

#### 反填示例

方案说明：

#### 入院记录

##### 填充入院记录

方法名称：

参数说明：

示例：

##### 填充入院记录的字段

方法名称：

参数说明：

示例：

#### 首次病程记录

##### 填充首次病程记录

方法名称：

参数说明：

示例：

##### 填充首次病程记录的字段

方法名称：

参数说明：

示例：

#### TNM分期记录

##### 填充TNM分期记录

方法名称：

参数说明：

示例：

##### 填充TNM分期记录的字段

方法名称：

参数说明：

示例：

#### 出院记录

##### 填充出院记录

方法名称：

参数说明：

示例：

##### 填充出院记录的字段

方法名称：

参数说明：

示例：

#### 医嘱信息

##### 填充医嘱信息记录

方法名称：

参数说明：

示例：


