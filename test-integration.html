<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>迎春花质控系统集成测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .section h2 {
            color: #666;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        input, select {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .patient-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px;
            display: inline-block;
            width: 250px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .patient-card:hover {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0,123,255,0.3);
        }
        .patient-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .patient-card p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌸 迎春花质控系统集成测试</h1>

        <!-- SDK状态 -->
        <div class="section">
            <h2>📡 SDK状态</h2>
            <div id="sdkStatus" class="status info">SDK未加载</div>
            <button onclick="loadSDK()">加载SDK</button>
            <button onclick="checkSDKStatus()">检查状态</button>
        </div>

        <!-- 配置参数 -->
        <div class="section">
            <h2>⚙️ 配置参数</h2>
            <div class="form-group">
                <label>科室ID:</label>
                <input type="text" id="deptId" value="0001" placeholder="请输入科室ID">
            </div>
            <div class="form-group">
                <label>医生ID:</label>
                <input type="text" id="doctorId" value="0002" placeholder="请输入医生ID">
            </div>
            <div class="form-group">
                <label>AppKey:</label>
                <input type="text" id="appKey" value="ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09" placeholder="应用密钥">
            </div>
            <div class="form-group">
                <label>AppSecretKey:</label>
                <input type="text" id="appSecretKey" value="YYCloud1644286723584" placeholder="应用秘密密钥">
            </div>
            <div class="form-group">
                <label>质控目标:</label>
                <select id="qualityTarget">
                    <option value="1">临床</option>
                    <option value="2" selected>病理</option>
                </select>
            </div>
            <button onclick="configureSDK()">配置SDK</button>
        </div>

        <!-- 模拟患者数据 -->
        <div class="section">
            <h2>👥 模拟患者数据</h2>
            <div id="patientCards">
                <!-- 住院患者 -->
                <div class="patient-card" onclick="sendPatientData('inpatient')">
                    <h3>张三 (住院)</h3>
                    <p><strong>性别:</strong> 男</p>
                    <p><strong>年龄:</strong> 45岁</p>
                    <p><strong>科室:</strong> 肿瘤内科</p>
                    <p><strong>床号:</strong> 28床</p>
                    <p><strong>患者ID:</strong> 00931222</p>
                    <p><strong>就诊ID:</strong> 00931222|4623477|1住院</p>
                </div>

                <!-- 门诊患者 -->
                <div class="patient-card" onclick="sendPatientData('outpatient')">
                    <h3>李四 (门诊)</h3>
                    <p><strong>性别:</strong> 女</p>
                    <p><strong>年龄:</strong> 38岁</p>
                    <p><strong>科室:</strong> 肿瘤科</p>
                    <p><strong>患者ID:</strong> OP001234</p>
                    <p><strong>就诊ID:</strong> OP001234|20230416001|2门诊</p>
                </div>
            </div>
            <button onclick="sendAllPatients()">批量发送所有患者</button>
        </div>

        <!-- 操作日志 -->
        <div class="section">
            <h2>📋 操作日志</h2>
            <button onclick="clearLog()">清空日志</button>
            <div id="logContainer" class="log"></div>
        </div>
    </div>

    <!-- 引入迎春花SDK -->
    <script src="http://**************:8094/client_app_iframe/index.js?linkType=2"></script>

    <script>
        let sdkReady = false;

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleString();
            const logContainer = document.getElementById('logContainer');
            const logEntry = `[${timestamp}] ${message}\n`;
            logContainer.textContent += logEntry;
            logContainer.scrollTop = logContainer.scrollHeight;

            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 更新SDK状态显示
        function updateSDKStatus(status, message) {
            const statusElement = document.getElementById('sdkStatus');
            statusElement.className = `status ${status}`;
            statusElement.textContent = message;
        }

        // 页面加载完成后检查SDK
        window.onload = function() {
            log('页面加载完成，开始检查SDK状态...');
            setTimeout(checkSDKStatus, 1000); // 延迟1秒检查，确保SDK有时间加载
        };

        // 检查SDK状态
        function checkSDKStatus() {
            if (typeof window._clientAppSDK !== 'undefined' && window._clientAppSDK) {
                sdkReady = true;
                updateSDKStatus('success', 'SDK已加载并就绪');
                log('✅ SDK检查成功，_clientAppSDK对象可用', 'success');
                log('SDK对象方法: ' + Object.keys(window._clientAppSDK).join(', '));
            } else {
                sdkReady = false;
                updateSDKStatus('error', 'SDK未加载或不可用');
                log('❌ SDK检查失败，_clientAppSDK对象不可用', 'error');
            }
        }

        // 手动加载SDK
        function loadSDK() {
            log('开始手动加载SDK...');
            updateSDKStatus('info', '正在加载SDK...');

            // 移除现有的script标签
            const existingScript = document.querySelector('script[src*="client_app_iframe"]');
            if (existingScript) {
                existingScript.remove();
            }

            // 创建新的script标签
            const script = document.createElement('script');
            script.src = 'http://**************:8094/client_app_iframe/index.js?linkType=2';
            script.onload = function() {
                log('SDK脚本加载完成');
                setTimeout(checkSDKStatus, 500);
            };
            script.onerror = function() {
                updateSDKStatus('error', 'SDK加载失败');
                log('❌ SDK脚本加载失败', 'error');
            };

            document.head.appendChild(script);
        }

        // 配置SDK参数
        function configureSDK() {
            if (!sdkReady) {
                alert('SDK未就绪，请先加载SDK');
                return;
            }

            const config = {
                deptId: document.getElementById('deptId').value,
                doctorId: document.getElementById('doctorId').value,
                appKey: document.getElementById('appKey').value,
                appSecretKey: document.getElementById('appSecretKey').value,
                qualityTarget: document.getElementById('qualityTarget').value
            };

            log('开始配置SDK参数: ' + JSON.stringify(config));

            try {
                // 调用SDK的changeParams方法
                window._clientAppSDK.changeParams(config);
                log('✅ SDK参数配置成功', 'success');
                updateSDKStatus('success', 'SDK已配置并就绪');
            } catch (error) {
                log('❌ SDK参数配置失败: ' + error.message, 'error');
                updateSDKStatus('error', 'SDK配置失败');
            }
        }

        // 发送患者数据
        function sendPatientData(patientType) {
            if (!sdkReady) {
                alert('SDK未就绪，请先加载并配置SDK');
                return;
            }

            let patientData;

            if (patientType === 'inpatient') {
                // 住院患者数据
                patientData = {
                    patientId: "88888888",
                    visitSn: "88888888",
                    dataPacket: [{
                        tableCode: "b02_1",
                        data: [{
                            patient_id: "88888888",
                            visit_sn: "88888888",
                            visit_type: "住院",
                            hospital_code: "25ab66f1a2a2421e9b45de3339c17a53",
                            hospital_name: "山西省肿瘤医院",
                            visit_card_no: "ttt1234",
                            medical_record_no: "**********",
                            inpatient_no: "102310",
                            hospitalization_times: "1",
                            admission_datetime: "2022-04-16 07:11:07",
                            discharge_datetime: "2022-04-16 07:11:07",
                            visit_doctor_no: "001346",
                            visit_doctor_name: "张文宏",
                            name: "张三",
                            gender: "男",
                            patient_gender: "1",
                            date_of_birth: "1987-05-25",
                            occupation_code: "",
                            occupation_name: "程序员",
                            nationality: "中国",
                            ethnicity: "汉族",
                            education: "小学毕业",
                            marital_status: "已婚",
                            newbron_mark: "否",
                            visit_status: "否",
                            patient_identity: "其他",
                            certificate_type: "身份证",
                            certificate_no: "xxxxxxxxxxxxxxxxxx",
                            idcard_no: "xxxxxxxxxxxxxxxxxx",
                            insurance_type: "云南省属地州职工医保",
                            insurance_no: "HZ2501085623",
                            phone_no: "18877887778",
                            abo_blood_type: "A型",
                            rh_blood_type: "阴性",
                            record_status: "1",
                            record_datetime: "2020-04-21 09:45:34",
                            admission_dept_code: "034",
                            admission_dept_name: "肿瘤内科",
                            admission_ward_code: "123",
                            admission_ward_name: "化疗病房",
                            admission_bed_name: "28床",
                            current_dept_code: "034",
                            current_dept_name: "肿瘤内科",
                            current_ward_code: "123",
                            current_ward_name: "化疗病房",
                            current_bed_name: "28床",
                            chief_physician_id: "456",
                            chief_physician: "王五",
                            attending_physician_id: "001346",
                            attending_physician: "张文宏",
                            admission_type_name: "急诊"
                        }]
                    }]
                };
            } else {
                // 门诊患者数据
                patientData = {
                    patientId: "OP001234",
                    visitSn: "OP001234|20230416001|2门诊",
                    dataPacket: [{
                        tableCode: "b12_1",
                        data: [{
                            patient_id: "OP001234",
                            visit_sn: "OP001234|20230416001|2门诊",
                            visit_type: "门诊",
                            hospital_code: "25ab66f1a2a2421e9b45de3339c17a53",
                            hospital_name: "山西省肿瘤医院",
                            outpatient_no: "20230416001",
                            visit_times: "1",
                            visit_datetime: "2020-04-21 09:45:34",
                            visit_doctor_no: "001346",
                            visit_doctor_name: "张医生",
                            name: "李四",
                            gender: "女",
                            patient_gender: "2",
                            date_of_birth: "1985-03-20",
                            occupation_code: "",
                            occupation_name: "教师",
                            nationality: "中国",
                            ethnicity: "汉族",
                            education: "大学本科",
                            marital_status: "已婚",
                            newbron_mark: "否",
                            visit_status: "否",
                            patient_identity: "其他",
                            certificate_type: "身份证",
                            certificate_no: "xxxxxxxxxxxxxxxxxx",
                            idcard_no: "xxxxxxxxxxxxxxxxxx",
                            insurance_type: "居民医保",
                            insurance_no: "JM2501085624",
                            phone_no: "13900139000",
                            abo_blood_type: "B型",
                            rh_blood_type: "阳性",
                            record_status: "1",
                            record_datetime: "2020-04-21 09:45:34",
                            regis_sn: "13423",
                            regis_datetime: "2020-04-21 09:45:34",
                            first_visit_mark: "是",
                            regis_method_code: "3",
                            regis_method: "现场预约",
                            regis_type_code: "3",
                            regis_type: "普通号",
                            regis_charge_price: "5.000",
                            regis_paid_price: "5.000",
                            regis_dept_code: "15",
                            regis_dept_name: "肿瘤科",
                            technical_title: "主治医师",
                            job_title: "科室主任"
                        }]
                    }]
                };
            }

            log(`开始发送${patientType === 'inpatient' ? '住院' : '门诊'}患者数据...`);
            log('患者数据: ' + JSON.stringify(patientData, null, 2));

            try {
                // 调用SDK的history方法发送患者数据
                window._clientAppSDK.history(patientData);
                log(`✅ ${patientType === 'inpatient' ? '住院' : '门诊'}患者数据发送成功`, 'success');
            } catch (error) {
                log(`❌ ${patientType === 'inpatient' ? '住院' : '门诊'}患者数据发送失败: ` + error.message, 'error');
            }
        }

        // 批量发送所有患者
        function sendAllPatients() {
            log('开始批量发送所有患者数据...');
            sendPatientData('inpatient');
            setTimeout(() => sendPatientData('outpatient'), 1000);
        }

        // 清空日志
        function clearLog() {
            document.getElementById('logContainer').textContent = '';
            log('日志已清空');
        }
    </script>
</body>
</html>
