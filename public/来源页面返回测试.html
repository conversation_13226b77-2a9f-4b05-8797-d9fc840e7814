<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>来源页面返回功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        h2 {
            color: #3498db;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .test-link {
            display: inline-block;
            padding: 12px 24px;
            margin: 10px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: background-color 0.3s;
        }
        
        .test-link:hover {
            background-color: #2980b9;
        }
        
        .test-link.secondary {
            background-color: #95a5a6;
        }
        
        .test-link.secondary:hover {
            background-color: #7f8c8d;
        }
        
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        
        .status-info {
            background-color: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
        }
        
        .warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
        }
        
        .success {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
        }
        
        .test-steps {
            counter-reset: step-counter;
        }
        
        .test-steps li {
            counter-increment: step-counter;
            margin-bottom: 10px;
            padding-left: 10px;
            position: relative;
        }
        
        .test-steps li::before {
            content: counter(step-counter);
            position: absolute;
            left: -25px;
            top: 0;
            background-color: #3498db;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .current-url {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔄 来源页面返回功能测试</h1>



    <div class="test-container">
        <h2>🧪 测试链接</h2>
        <p>点击以下链接测试来源页面返回功能：</p>
        
        <div style="margin: 20px 0;">

            <a href="#" onclick="openTestLink('ych')" class="test-link">
                🏠 跳转到系统首页（迎春花数据源）
            </a>

            <a href="#" onclick="openTestLink('xrk')" class="test-link">
                🌻 跳转到系统首页（向日葵数据源）
            </a>
        </div>

        <div id="dynamicLinkContainer"></div>
    </div>



    <script>
        // 显示当前URL
        document.getElementById('currentUrl').textContent = window.location.href;
        
        // 打开测试链接
        function openTestLink(source) {
            const currentUrl = window.location.href;
            const encodedReferrer = encodeURIComponent(currentUrl);

            let testUrl;
            if (source === 'ych') {
                testUrl = `/?referrer=${encodedReferrer}`;
            } else {
                testUrl = `/?source=${source}&referrer=${encodedReferrer}`;
            }

            console.log('🚀 准备跳转到:', testUrl);
            console.log('📍 当前页面URL:', currentUrl);
            console.log('🔗 编码后的referrer:', encodedReferrer);

            window.open(testUrl, '_blank');
        }

        // 动态生成测试链接
        function generateDynamicLink() {
            const currentUrl = window.location.href;
            const encodedReferrer = encodeURIComponent(currentUrl);
            const timestamp = new Date().getTime();

            const testUrl = `/?source=xrk&referrer=${encodedReferrer}&t=${timestamp}`;

            const container = document.getElementById('dynamicLinkContainer');
            container.innerHTML = `
                <div class="status-info">
                    <strong>动态生成的测试链接：</strong>
                    <div class="current-url">${testUrl}</div>
                    <a href="#" onclick="window.open('${testUrl}', '_blank')" class="test-link" style="margin-top: 10px;">
                        🎯 使用动态链接测试
                    </a>
                </div>
            `;
        }
        
        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('🧪 来源页面返回功能测试页面已加载');
            console.log('📍 当前页面URL:', window.location.href);
            console.log('💡 请点击测试链接开始测试');

            // 实时更新URL显示
            updateCurrentUrl();
        });

        // 更新当前URL显示
        function updateCurrentUrl() {
            const currentUrl = window.location.href;
            document.getElementById('currentUrl').textContent = currentUrl;

            // 解析URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const referrer = urlParams.get('referrer');

            if (referrer) {
                console.log('🔍 检测到referrer参数:', referrer);
                console.log('🔍 解码后的referrer:', decodeURIComponent(referrer));
            }
        }
        
        // 监听页面可见性变化，用于检测是否从系统返回
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                console.log('🔄 页面重新可见，可能是从系统返回');

                // 更新URL显示
                updateCurrentUrl();
            }
        });

        // 监听URL变化（用于单页应用）
        let lastUrl = window.location.href;
        setInterval(() => {
            const currentUrl = window.location.href;
            if (currentUrl !== lastUrl) {
                lastUrl = currentUrl;
                updateCurrentUrl();
                console.log('🔄 URL已变化:', currentUrl);
            }
        }, 1000);
    </script>
</body>
</html>
