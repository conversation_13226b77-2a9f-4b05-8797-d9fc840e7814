import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  // 获取后端服务器地址
  const backendUrl = env.VITE_BACKEND_URL || 'http://localhost:6596'

  console.log(`🔧 Vite配置 - 模式: ${mode}`)
  console.log(`🌐 后端服务器: ${backendUrl}`)
  console.log(`📡 API代理: /api -> ${backendUrl}`)

  return {
    plugins: [vue()],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    },
    server: {
      port: 5173,
      host: true, // 允许外部访问
      proxy: {
        // 代理所有以 /api 开头的请求到后端服务器
        '/api': {
          target: backendUrl,
          changeOrigin: true,
          secure: false, // 如果是https接口，需要配置这个参数
          rewrite: (path) => path.replace(/^\/api/, ''),
          configure: (proxy, options) => {
            proxy.on('error', (err, req, res) => {
              console.error('❌ 代理错误:', err.message);
            });
            proxy.on('proxyReq', (proxyReq, req, res) => {
              console.log(`📤 代理请求: ${req.method} ${req.url} -> ${backendUrl}${req.url.replace('/api', '')}`);
            });
            proxy.on('proxyRes', (proxyRes, req, res) => {
              console.log(`📥 代理响应: ${proxyRes.statusCode} ${req.url} (${proxyRes.headers['content-length'] || 0} bytes)`);
            });
          }
        }
      }
    },
    // 构建配置
    build: {
      outDir: 'dist',
      sourcemap: mode === 'development',
      // 代码分割优化
      rollupOptions: {
        output: {
          // 手动分割代码块
          manualChunks: {
            // Vue核心库
            'vue-vendor': ['vue', 'vue-router', 'pinia'],
            // Element Plus UI库
            'element-plus': ['element-plus'],
            // 工具库
            'utils': ['axios'],
            // 医疗业务模块（预留）
            // 'medical': ['@/views/medical/DepartmentList.vue', '@/views/medical/PatientManagement.vue']
          },
          // 文件命名策略
          chunkFileNames: (chunkInfo) => {
            const facadeModuleId = chunkInfo.facadeModuleId
            if (facadeModuleId) {
              // 根据文件路径生成chunk名称
              if (facadeModuleId.includes('views/')) {
                return 'pages/[name]-[hash].js'
              }
              if (facadeModuleId.includes('components/')) {
                return 'components/[name]-[hash].js'
              }
              if (facadeModuleId.includes('stores/')) {
                return 'stores/[name]-[hash].js'
              }
            }
            return 'chunks/[name]-[hash].js'
          },
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            // 资源文件命名
            if (assetInfo.name?.endsWith('.css')) {
              return 'css/[name]-[hash].css'
            }
            if (/\.(png|jpe?g|gif|svg|webp|ico)$/.test(assetInfo.name || '')) {
              return 'images/[name]-[hash].[ext]'
            }
            if (/\.(woff2?|eot|ttf|otf)$/.test(assetInfo.name || '')) {
              return 'fonts/[name]-[hash].[ext]'
            }
            return 'assets/[name]-[hash].[ext]'
          }
        }
      },
      // 压缩配置
      minify: 'terser',
      terserOptions: {
        compress: {
          // 生产环境移除console
          drop_console: mode === 'production',
          drop_debugger: mode === 'production'
        }
      },
      // 分块大小警告阈值
      chunkSizeWarningLimit: 1000,
      // 启用CSS代码分割
      cssCodeSplit: true
    },

    // 性能优化
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'element-plus',
        'axios'
      ],
      // 预构建排除
      exclude: []
    }
  }
})
